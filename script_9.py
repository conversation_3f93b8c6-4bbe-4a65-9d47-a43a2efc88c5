# Créer la page de monitoring et surveillance
monitor_page_content = '''"""
Page de monitoring système et surveillance des erreurs
Surveillance en temps réel avec graphiques et alertes
"""
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import time
import sys
import os

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from config import *
from utils.monitoring import monitor, SystemMonitor
from utils.ollama_api import OllamaAPI
from utils.n8n_api import N8nAPI

st.set_page_config(
    page_title="Monitoring Système",
    page_icon="📊",
    layout="wide"
)

# CSS personnalisé
st.markdown("""
<style>
    .metric-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        text-align: center;
        margin: 0.5rem 0;
    }
    
    .metric-good {
        border-left: 5px solid #4CAF50;
    }
    
    .metric-warning {
        border-left: 5px solid #FF9800;
    }
    
    .metric-critical {
        border-left: 5px solid #f44336;
    }
    
    .alert-container {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-left: 4px solid #f39c12;
        padding: 1rem;
        border-radius: 8px;
        margin: 0.5rem 0;
    }
    
    .alert-high {
        background: #f8d7da;
        border-color: #f5c6cb;
        border-left-color: #dc3545;
    }
    
    .alert-medium {
        background: #fff3cd;
        border-color: #ffeaa7;
        border-left-color: #f39c12;
    }
    
    .process-item {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        margin: 0.5rem 0;
    }
    
    .system-info {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 12px;
        margin: 1rem 0;
    }
    
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .status-online { background-color: #4CAF50; }
    .status-offline { background-color: #f44336; }
    .status-warning { background-color: #FF9800; }
</style>
""", unsafe_allow_html=True)

def init_session_state():
    """Initialise les variables de session"""
    if 'ollama_api' not in st.session_state:
        st.session_state.ollama_api = OllamaAPI()
    
    if 'n8n_api' not in st.session_state:
        st.session_state.n8n_api = N8nAPI()
    
    if 'auto_refresh' not in st.session_state:
        st.session_state.auto_refresh = False
    
    if 'refresh_interval' not in st.session_state:
        st.session_state.refresh_interval = 5

def get_metric_status(value: float, warning_threshold: float, critical_threshold: float):
    """Détermine le statut d'une métrique"""
    if value >= critical_threshold:
        return "critical", "🔴"
    elif value >= warning_threshold:
        return "warning", "🟡"
    else:
        return "good", "🟢"

def display_system_overview():
    """Affiche la vue d'ensemble du système"""
    st.subheader("🖥️ Vue d'Ensemble du Système")
    
    # Récupérer les métriques actuelles
    metrics = monitor.get_current_metrics()
    
    col1, col2, col3, col4 = st.columns(4)
    
    # CPU
    with col1:
        cpu_status, cpu_icon = get_metric_status(metrics.cpu_percent, 70, 90)
        st.markdown(f"""
        <div class="metric-card metric-{cpu_status}">
            <h3>{cpu_icon} CPU</h3>
            <h2>{metrics.cpu_percent:.1f}%</h2>
            <p>Utilisation processeur</p>
        </div>
        """, unsafe_allow_html=True)
    
    # Mémoire
    with col2:
        mem_status, mem_icon = get_metric_status(metrics.memory_percent, 80, 95)
        st.markdown(f"""
        <div class="metric-card metric-{mem_status}">
            <h3>{mem_icon} Mémoire</h3>
            <h2>{metrics.memory_percent:.1f}%</h2>
            <p>Utilisation RAM</p>
        </div>
        """, unsafe_allow_html=True)
    
    # Disque
    with col3:
        disk_status, disk_icon = get_metric_status(metrics.disk_usage, 85, 95)
        st.markdown(f"""
        <div class="metric-card metric-{disk_status}">
            <h3>{disk_icon} Disque</h3>
            <h2>{metrics.disk_usage:.1f}%</h2>
            <p>Utilisation stockage</p>
        </div>
        """, unsafe_allow_html=True)
    
    # Processus
    with col4:
        st.markdown(f"""
        <div class="metric-card metric-good">
            <h3>⚙️ Processus</h3>
            <h2>{metrics.process_count}</h2>
            <p>Processus actifs</p>
        </div>
        """, unsafe_allow_html=True)

def display_services_status():
    """Affiche le statut des services"""
    st.subheader("🔧 Statut des Services")
    
    metrics = monitor.get_current_metrics()
    ollama_connected = st.session_state.ollama_api.is_connected()
    n8n_connected = st.session_state.n8n_api.is_connected()
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        ollama_status = "online" if ollama_connected and metrics.ollama_running else "offline"
        ollama_text = "En ligne" if ollama_status == "online" else "Hors ligne"
        
        st.markdown(f"""
        <div class="metric-card">
            <span class="status-indicator status-{ollama_status}"></span>
            <strong>Ollama:</strong> {ollama_text}<br>
            <small>Port 11434 • API REST</small>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        n8n_status = "online" if n8n_connected and metrics.n8n_running else "offline"
        n8n_text = "En ligne" if n8n_status == "online" else "Hors ligne"
        
        st.markdown(f"""
        <div class="metric-card">
            <span class="status-indicator status-{n8n_status}"></span>
            <strong>n8n:</strong> {n8n_text}<br>
            <small>Port 5678 • Workflows</small>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        # Statut réseau
        network_status = "online" if metrics.network_sent > 0 or metrics.network_recv > 0 else "warning"
        network_text = "Actif" if network_status == "online" else "Faible activité"
        
        st.markdown(f"""
        <div class="metric-card">
            <span class="status-indicator status-{network_status}"></span>
            <strong>Réseau:</strong> {network_text}<br>
            <small>↑ {metrics.network_sent/1024:.1f} KB/s ↓ {metrics.network_recv/1024:.1f} KB/s</small>
        </div>
        """, unsafe_allow_html=True)

def display_real_time_charts():
    """Affiche les graphiques en temps réel"""
    st.subheader("📈 Métriques Temps Réel")
    
    # Récupérer les données pour les graphiques
    cpu_data = monitor.get_metrics_for_chart("cpu", last_n=30)
    memory_data = monitor.get_metrics_for_chart("memory", last_n=30)
    
    if cpu_data and memory_data:
        col1, col2 = st.columns(2)
        
        with col1:
            # Graphique CPU
            cpu_df = pd.DataFrame(cpu_data)
            fig_cpu = px.line(
                cpu_df, 
                x='time', 
                y='value',
                title='Utilisation CPU (%)',
                line_shape='spline'
            )
            fig_cpu.update_layout(
                yaxis_range=[0, 100],
                showlegend=False,
                height=300
            )
            fig_cpu.add_hline(y=CPU_THRESHOLD, line_dash="dash", line_color="orange", annotation_text="Seuil d'alerte")
            st.plotly_chart(fig_cpu, use_container_width=True)
        
        with col2:
            # Graphique Mémoire
            memory_df = pd.DataFrame(memory_data)
            fig_memory = px.line(
                memory_df,
                x='time',
                y='value', 
                title='Utilisation Mémoire (%)',
                line_shape='spline'
            )
            fig_memory.update_layout(
                yaxis_range=[0, 100],
                showlegend=False,
                height=300
            )
            fig_memory.add_hline(y=MEMORY_THRESHOLD, line_dash="dash", line_color="orange", annotation_text="Seuil d'alerte")
            st.plotly_chart(fig_memory, use_container_width=True)
    
    # Graphique réseau
    network_data = monitor.get_metrics_for_chart("network", last_n=30)
    if network_data:
        network_df = pd.DataFrame(network_data)
        
        fig_network = go.Figure()
        fig_network.add_trace(go.Scatter(
            x=network_df['time'],
            y=network_df['sent'],
            mode='lines',
            name='Envoyé (KB/s)',
            line=dict(color='blue')
        ))
        fig_network.add_trace(go.Scatter(
            x=network_df['time'],
            y=network_df['recv'],
            mode='lines',
            name='Reçu (KB/s)',
            line=dict(color='green')
        ))
        
        fig_network.update_layout(
            title='Trafic Réseau',
            xaxis_title='Temps',
            yaxis_title='KB/s',
            height=300
        )
        
        st.plotly_chart(fig_network, use_container_width=True)

def display_alerts_section():
    """Affiche la section des alertes"""
    st.subheader("🚨 Alertes et Notifications")
    
    alerts = monitor.get_recent_alerts(limit=10)
    
    if alerts:
        for alert in alerts:
            severity_class = f"alert-{alert.get('severity', 'medium')}"
            timestamp = alert['timestamp'].strftime("%H:%M:%S")
            
            icon = "🔴" if alert['severity'] == 'high' else "🟡" if alert['severity'] == 'medium' else "🔵"
            
            st.markdown(f"""
            <div class="alert-container {severity_class}">
                {icon} <strong>{timestamp}</strong> - {alert['message']}
            </div>
            """, unsafe_allow_html=True)
    else:
        st.success("✅ Aucune alerte active")

def display_process_monitor():
    """Affiche le monitoring des processus"""
    st.subheader("🔄 Processus les Plus Gourmands")
    
    processes = monitor.get_process_info(limit=10)
    
    if processes:
        for proc in processes:
            cpu_percent = proc.get('cpu_percent', 0)
            memory_percent = proc.get('memory_percent', 0)
            name = proc.get('name', 'Inconnu')
            pid = proc.get('pid', 'N/A')
            
            st.markdown(f"""
            <div class="process-item">
                <strong>{name}</strong> (PID: {pid})<br>
                CPU: {cpu_percent:.1f}% • Mémoire: {memory_percent:.1f}%
            </div>
            """, unsafe_allow_html=True)
    else:
        st.info("Aucun processus gourmand détecté")

def display_system_information():
    """Affiche les informations système"""
    st.subheader("💻 Informations Système")
    
    system_info = monitor.get_system_info()
    
    st.markdown(f"""
    <div class="system-info">
        <h4>Configuration Système</h4>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
            <div>
                <strong>CPU:</strong> {system_info.get('cpu_cores', 'N/A')} cœurs<br>
                <strong>Fréquence:</strong> {system_info.get('cpu_frequency', 'N/A')}<br>
                <strong>Mémoire Totale:</strong> {system_info.get('total_memory', 'N/A')}
            </div>
            <div>
                <strong>Disque Total:</strong> {system_info.get('total_disk', 'N/A')}<br>
                <strong>Démarrage:</strong> {system_info.get('boot_time', 'N/A')}<br>
                <strong>Uptime:</strong> {system_info.get('uptime', 'N/A')}
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

def display_monitoring_controls():
    """Affiche les contrôles de monitoring"""
    st.subheader("⚙️ Contrôles de Monitoring")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        auto_refresh = st.checkbox(
            "🔄 Actualisation automatique",
            value=st.session_state.auto_refresh
        )
        st.session_state.auto_refresh = auto_refresh
    
    with col2:
        refresh_interval = st.selectbox(
            "Intervalle (secondes)",
            options=[2, 5, 10, 30, 60],
            index=1
        )
        st.session_state.refresh_interval = refresh_interval
    
    with col3:
        if st.button("🗑️ Effacer Alertes", use_container_width=True):
            monitor.clear_alerts()
            st.success("Alertes effacées!")
            st.rerun()

def display_export_section():
    """Affiche la section d'export des données"""
    st.subheader("📥 Export des Données")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("📊 Exporter Métriques CSV", use_container_width=True):
            csv_data = monitor.export_metrics_to_csv()
            if csv_data:
                st.download_button(
                    label="Télécharger CSV",
                    data=csv_data,
                    file_name=f"metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )
            else:
                st.warning("Aucune donnée à exporter")
    
    with col2:
        if st.button("📋 Rapport Système", use_container_width=True):
            # Générer un rapport texte
            metrics = monitor.get_current_metrics()
            system_info = monitor.get_system_info()
            
            report = f"""# Rapport Système - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Métriques Actuelles
- CPU: {metrics.cpu_percent:.1f}%
- Mémoire: {metrics.memory_percent:.1f}%
- Disque: {metrics.disk_usage:.1f}%
- Processus: {metrics.process_count}

## Services
- Ollama: {'✅' if metrics.ollama_running else '❌'}
- n8n: {'✅' if metrics.n8n_running else '❌'}

## Configuration Système
- CPU Cœurs: {system_info.get('cpu_cores', 'N/A')}
- Mémoire Totale: {system_info.get('total_memory', 'N/A')}
- Disque Total: {system_info.get('total_disk', 'N/A')}
- Uptime: {system_info.get('uptime', 'N/A')}
"""
            
            st.download_button(
                label="Télécharger Rapport",
                data=report,
                file_name=f"rapport_systeme_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
                mime="text/markdown"
            )

def main():
    """Fonction principale de la page de monitoring"""
    init_session_state()
    
    st.title("📊 Monitoring Système")
    
    # Contrôles de monitoring
    display_monitoring_controls()
    
    # Auto-refresh logic
    if st.session_state.auto_refresh:
        time.sleep(st.session_state.refresh_interval)
        st.rerun()
    
    st.markdown("---")
    
    # Vue d'ensemble système
    display_system_overview()
    
    st.markdown("---")
    
    # Statut des services
    display_services_status()
    
    st.markdown("---")
    
    # Graphiques temps réel
    display_real_time_charts()
    
    st.markdown("---")
    
    # Alertes
    display_alerts_section()
    
    st.markdown("---")
    
    # Layout en colonnes pour processus et infos système
    col1, col2 = st.columns(2)
    
    with col1:
        display_process_monitor()
    
    with col2:
        display_system_information()
    
    st.markdown("---")
    
    # Section d'export
    display_export_section()

if __name__ == "__main__":
    main()
'''

with open("dashboard_app/pages/04_📊_Monitor.py", "w", encoding='utf-8') as f:
    f.write(monitor_page_content)

print("✅ Page monitoring système créée")