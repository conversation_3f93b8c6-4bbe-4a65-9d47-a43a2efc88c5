"""
Page de monitoring système
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
from datetime import datetime, timedelta
import time
import json

from config import MONITORING_CONFIG, CHART_COLORS, EXPORT_CONFIG
from monitoring import system_monitor
from ollama_api import ollama_api
from n8n_api import n8n_api

# Configuration de la page
st.set_page_config(
    page_title="📊 Monitoring Système",
    page_icon="📊",
    layout="wide"
)

def initialize_session_state():
    """Initialise les variables de session pour le monitoring"""
    if "auto_refresh" not in st.session_state:
        st.session_state.auto_refresh = False

    if "refresh_interval" not in st.session_state:
        st.session_state.refresh_interval = 5

    if "alert_history" not in st.session_state:
        st.session_state.alert_history = []

def display_sidebar():
    """Affiche la barre latérale avec les paramètres de monitoring"""
    with st.sidebar:
        st.header("⚙️ Paramètres de Monitoring")

        # Actualisation automatique
        st.session_state.auto_refresh = st.checkbox(
            "🔄 Actualisation automatique",
            value=st.session_state.auto_refresh,
            help="Active l'actualisation automatique des métriques"
        )

        if st.session_state.auto_refresh:
            st.session_state.refresh_interval = st.slider(
                "Intervalle (secondes):",
                min_value=1,
                max_value=60,
                value=st.session_state.refresh_interval,
                help="Fréquence d'actualisation en secondes"
            )

        st.divider()

        # Seuils d'alerte personnalisés
        st.subheader("🚨 Seuils d'Alerte")

        cpu_threshold = st.slider(
            "CPU (%)",
            min_value=50,
            max_value=100,
            value=MONITORING_CONFIG["cpu_threshold"],
            help="Seuil d'alerte pour l'utilisation CPU"
        )

        memory_threshold = st.slider(
            "Mémoire (%)",
            min_value=50,
            max_value=100,
            value=MONITORING_CONFIG["memory_threshold"],
            help="Seuil d'alerte pour l'utilisation mémoire"
        )

        disk_threshold = st.slider(
            "Disque (%)",
            min_value=50,
            max_value=100,
            value=MONITORING_CONFIG["disk_threshold"],
            help="Seuil d'alerte pour l'utilisation disque"
        )

        # Mise à jour des seuils dans le système de monitoring
        system_monitor.update_thresholds(
            cpu_threshold=cpu_threshold,
            memory_threshold=memory_threshold,
            disk_threshold=disk_threshold
        )

        st.divider()

        # Actions
        st.subheader("📋 Actions")

        if st.button("🔄 Actualiser Maintenant"):
            system_monitor.update_metrics()
            st.success("Métriques actualisées!")
            st.rerun()

        if st.button("📥 Exporter Métriques"):
            export_metrics()

        if st.button("🗑️ Effacer Historique"):
            system_monitor.clear_history()
            st.session_state.alert_history = []
            st.success("Historique effacé!")

def display_system_overview():
    """Affiche un aperçu général du système"""
    st.subheader("🖥️ Aperçu Système")

    # Mise à jour des métriques
    system_monitor.update_metrics()

    # Informations système
    system_info = system_monitor.get_system_info()

    col1, col2 = st.columns(2)

    with col1:
        st.write("**Informations générales:**")
        st.write(f"• Système: {system_info.get('system', 'Inconnu')}")
        st.write(f"• Architecture: {system_info.get('architecture', 'Inconnu')}")
        st.write(f"• Processeur: {system_info.get('processor', 'Inconnu')}")
        st.write(f"• Cœurs CPU: {system_info.get('cpu_count', 'Inconnu')}")

    with col2:
        st.write("**Mémoire:**")
        memory_info = system_info.get('memory', {})
        if memory_info:
            total_gb = memory_info.get('total', 0) / (1024**3)
            st.write(f"• Mémoire totale: {total_gb:.1f} GB")
            st.write(f"• Mémoire disponible: {memory_info.get('available', 0) / (1024**3):.1f} GB")

        st.write("**Réseau:**")
        st.write(f"• Nom d'hôte: {system_info.get('hostname', 'Inconnu')}")
        st.write(f"• Adresse IP: {system_info.get('ip_address', 'Inconnu')}")

def display_real_time_metrics():
    """Affiche les métriques en temps réel"""
    st.subheader("📊 Métriques en Temps Réel")

    # Récupération des métriques actuelles
    cpu_usage = system_monitor.get_cpu_usage()
    memory_usage = system_monitor.get_memory_usage()
    disk_usage = system_monitor.get_disk_usage()
    network_usage = system_monitor.get_network_usage()

    # Affichage des métriques principales
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        # CPU avec indicateur de couleur
        cpu_color = "normal"
        if cpu_usage > MONITORING_CONFIG["cpu_threshold"]:
            cpu_color = "inverse"

        st.metric(
            label="🔥 CPU",
            value=f"{cpu_usage:.1f}%",
            delta=None
        )

        # Barre de progression
        st.progress(cpu_usage / 100)

    with col2:
        # Mémoire
        memory_percent = memory_usage.get("percentage", 0)
        memory_color = "normal"
        if memory_percent > MONITORING_CONFIG["memory_threshold"]:
            memory_color = "inverse"

        st.metric(
            label="🧠 Mémoire",
            value=f"{memory_percent:.1f}%",
            delta=None
        )

        st.progress(memory_percent / 100)

        # Détails mémoire
        if memory_usage:
            used_gb = memory_usage.get("used", 0) / (1024**3)
            total_gb = memory_usage.get("total", 0) / (1024**3)
            st.caption(f"{used_gb:.1f} GB / {total_gb:.1f} GB")

    with col3:
        # Disque (moyenne de tous les disques)
        if disk_usage:
            avg_disk = sum(d.get("percentage", 0) for d in disk_usage) / len(disk_usage)
            disk_color = "normal"
            if avg_disk > MONITORING_CONFIG["disk_threshold"]:
                disk_color = "inverse"

            st.metric(
                label="💾 Disque",
                value=f"{avg_disk:.1f}%",
                delta=None
            )

            st.progress(avg_disk / 100)

            # Détails du disque principal
            if disk_usage:
                main_disk = disk_usage[0]
                used_gb = main_disk.get("used", 0) / (1024**3)
                total_gb = main_disk.get("total", 0) / (1024**3)
                st.caption(f"{used_gb:.1f} GB / {total_gb:.1f} GB")

    with col4:
        # Réseau
        network_total = network_usage.get("kb_sent_per_sec", 0) + network_usage.get("kb_recv_per_sec", 0)

        st.metric(
            label="🌐 Réseau",
            value=f"{network_total:.1f} KB/s",
            delta=None
        )

        # Détails réseau
        sent = network_usage.get("kb_sent_per_sec", 0)
        recv = network_usage.get("kb_recv_per_sec", 0)
        st.caption(f"↑ {sent:.1f} KB/s")
        st.caption(f"↓ {recv:.1f} KB/s")

def display_historical_charts():
    """Affiche les graphiques historiques"""
    st.subheader("📈 Graphiques Historiques")

    # Récupération de l'historique
    history = system_monitor.get_history()

    if not history:
        st.info("Pas assez de données historiques. Laissez le monitoring tourner quelques minutes.")
        return

    # Préparation des données
    timestamps = [entry["timestamp"] for entry in history]
    cpu_data = [entry["cpu"] for entry in history]
    memory_data = [entry["memory"]["percentage"] for entry in history]

    # Graphique CPU et Mémoire
    fig = go.Figure()

    fig.add_trace(go.Scatter(
        x=timestamps,
        y=cpu_data,
        mode='lines',
        name='CPU (%)',
        line=dict(color=CHART_COLORS["primary"])
    ))

    fig.add_trace(go.Scatter(
        x=timestamps,
        y=memory_data,
        mode='lines',
        name='Mémoire (%)',
        line=dict(color=CHART_COLORS["secondary"])
    ))

    # Lignes de seuil
    fig.add_hline(
        y=MONITORING_CONFIG["cpu_threshold"],
        line_dash="dash",
        line_color=CHART_COLORS["warning"],
        annotation_text="Seuil CPU"
    )

    fig.add_hline(
        y=MONITORING_CONFIG["memory_threshold"],
        line_dash="dash",
        line_color=CHART_COLORS["error"],
        annotation_text="Seuil Mémoire"
    )

    fig.update_layout(
        title="Évolution CPU et Mémoire",
        xaxis_title="Temps",
        yaxis_title="Utilisation (%)",
        height=400,
        yaxis=dict(range=[0, 100])
    )

    st.plotly_chart(fig, use_container_width=True)

    # Graphique réseau
    if history and "network" in history[0]:
        network_sent = [entry["network"].get("kb_sent_per_sec", 0) for entry in history]
        network_recv = [entry["network"].get("kb_recv_per_sec", 0) for entry in history]

        fig_network = go.Figure()

        fig_network.add_trace(go.Scatter(
            x=timestamps,
            y=network_sent,
            mode='lines',
            name='Envoyé (KB/s)',
            line=dict(color=CHART_COLORS["success"])
        ))

        fig_network.add_trace(go.Scatter(
            x=timestamps,
            y=network_recv,
            mode='lines',
            name='Reçu (KB/s)',
            line=dict(color=CHART_COLORS["info"])
        ))

        fig_network.update_layout(
            title="Trafic Réseau",
            xaxis_title="Temps",
            yaxis_title="Débit (KB/s)",
            height=400
        )

        st.plotly_chart(fig_network, use_container_width=True)

def display_alerts():
    """Affiche les alertes système"""
    st.subheader("🚨 Alertes Système")

    # Récupération des alertes
    alerts = system_monitor.get_alerts()

    if alerts:
        # Affichage des alertes récentes
        st.write(f"**{len(alerts)} alerte(s) détectée(s)**")

        for alert in alerts[-10:]:  # 10 dernières alertes
            alert_time = alert["timestamp"].strftime("%H:%M:%S")

            # Couleur selon le type d'alerte
            if "CPU" in alert["message"]:
                st.error(f"{alert['icon']} **{alert_time}** - {alert['message']}")
            elif "Mémoire" in alert["message"]:
                st.warning(f"{alert['icon']} **{alert_time}** - {alert['message']}")
            else:
                st.info(f"{alert['icon']} **{alert_time}** - {alert['message']}")
    else:
        st.success("✅ Aucune alerte système")

def display_services_status():
    """Affiche le statut des services"""
    st.subheader("🔌 Statut des Services")

    col1, col2 = st.columns(2)

    with col1:
        st.write("**Services principaux:**")

        # Statut Ollama
        ollama_status = system_monitor.check_service_status("ollama")
        if ollama_status:
            st.success("🟢 Ollama - Actif")
        else:
            st.error("🔴 Ollama - Inactif")

        # Statut n8n
        n8n_status = system_monitor.check_service_status("n8n")
        if n8n_status:
            st.success("🟢 n8n - Actif")
        else:
            st.error("🔴 n8n - Inactif")

    with col2:
        st.write("**Informations de connexion:**")

        # Test de connectivité Ollama
        if ollama_api.is_available():
            models = ollama_api.get_models()
            st.write(f"• Ollama: {len(models)} modèle(s) disponible(s)")
        else:
            st.write("• Ollama: Non accessible")

        # Test de connectivité n8n
        if n8n_api.is_available():
            workflows = n8n_api.get_workflows()
            st.write(f"• n8n: {len(workflows)} workflow(s) disponible(s)")
        else:
            st.write("• n8n: Non accessible")

def display_disk_details():
    """Affiche les détails des disques"""
    st.subheader("💾 Détails des Disques")

    disk_usage = system_monitor.get_disk_usage()

    if disk_usage:
        for i, disk in enumerate(disk_usage):
            col1, col2, col3 = st.columns([2, 1, 1])

            with col1:
                mount_point = disk.get("mountpoint", f"Disque {i+1}")
                st.write(f"**{mount_point}**")

                # Barre de progression
                percentage = disk.get("percentage", 0)
                st.progress(percentage / 100)

            with col2:
                used_gb = disk.get("used", 0) / (1024**3)
                total_gb = disk.get("total", 0) / (1024**3)
                st.write(f"{used_gb:.1f} GB / {total_gb:.1f} GB")

            with col3:
                st.write(f"{percentage:.1f}%")

                # Indicateur de couleur
                if percentage > MONITORING_CONFIG["disk_threshold"]:
                    st.error("⚠️ Seuil dépassé")
                elif percentage > MONITORING_CONFIG["disk_threshold"] * 0.8:
                    st.warning("⚡ Attention")
                else:
                    st.success("✅ OK")

def export_metrics():
    """Exporte les métriques système"""
    # Récupération des données
    history = system_monitor.get_history()
    current_metrics = {
        "timestamp": datetime.now().isoformat(),
        "cpu": system_monitor.get_cpu_usage(),
        "memory": system_monitor.get_memory_usage(),
        "disk": system_monitor.get_disk_usage(),
        "network": system_monitor.get_network_usage(),
        "system_info": system_monitor.get_system_info()
    }

    # Préparation des données d'export
    export_data = {
        "export_timestamp": datetime.now().isoformat(),
        "current_metrics": current_metrics,
        "historical_data": history,
        "alerts": system_monitor.get_alerts()
    }

    # Export JSON
    json_str = json.dumps(export_data, indent=2, ensure_ascii=False, default=str)

    st.download_button(
        label="📥 Télécharger Métriques JSON",
        data=json_str,
        file_name=f"system_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
        mime="application/json"
    )

def main():
    """Fonction principale de la page"""

    # Initialisation
    initialize_session_state()

    st.title("📊 Monitoring Système")

    # Affichage de la barre latérale
    display_sidebar()

    # Auto-refresh
    if st.session_state.auto_refresh:
        time.sleep(st.session_state.refresh_interval)
        st.rerun()

    # Onglets
    tab1, tab2, tab3, tab4 = st.tabs(["📊 Temps Réel", "📈 Historique", "🚨 Alertes", "🔧 Détails"])

    with tab1:
        # Aperçu système
        display_system_overview()

        st.divider()

        # Métriques en temps réel
        display_real_time_metrics()

        st.divider()

        # Statut des services
        display_services_status()

    with tab2:
        # Graphiques historiques
        display_historical_charts()

    with tab3:
        # Alertes
        display_alerts()

        st.divider()

        # Configuration des alertes
        st.subheader("⚙️ Configuration des Alertes")
        st.info("Les seuils d'alerte peuvent être configurés dans la barre latérale.")

        # Statistiques des alertes
        alerts = system_monitor.get_alerts()
        if alerts:
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("Total alertes", len(alerts))

            with col2:
                # Alertes des dernières 24h
                recent_alerts = [
                    a for a in alerts
                    if (datetime.now() - a["timestamp"]).total_seconds() < 86400
                ]
                st.metric("Dernières 24h", len(recent_alerts))

            with col3:
                # Dernière alerte
                if alerts:
                    last_alert = alerts[-1]["timestamp"]
                    time_diff = datetime.now() - last_alert
                    if time_diff.total_seconds() < 3600:
                        st.metric("Dernière alerte", f"Il y a {int(time_diff.total_seconds() / 60)} min")
                    else:
                        st.metric("Dernière alerte", f"Il y a {int(time_diff.total_seconds() / 3600)} h")

    with tab4:
        # Détails des disques
        display_disk_details()

        st.divider()

        # Informations système détaillées
        st.subheader("🖥️ Informations Système Détaillées")
        system_info = system_monitor.get_system_info()

        # Affichage sous forme de JSON formaté
        st.json(system_info)

if __name__ == "__main__":
    main()