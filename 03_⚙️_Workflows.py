"""
Page de gestion des workflows n8n
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
from datetime import datetime, timedelta
import json

from config import DEFAULT_MESSAGES, CHART_COLORS
from n8n_api import n8n_api

# Configuration de la page
st.set_page_config(
    page_title="⚙️ Gestion des Workflows",
    page_icon="⚙️",
    layout="wide"
)

def format_datetime(dt_string):
    """Formate une date/heure pour l'affichage"""
    try:
        dt = datetime.fromisoformat(dt_string.replace('Z', '+00:00'))
        return dt.strftime('%d/%m/%Y %H:%M:%S')
    except:
        return dt_string

def get_status_icon(status):
    """Retourne l'icône correspondant au statut"""
    status_icons = {
        "success": "✅",
        "error": "❌",
        "running": "🔄",
        "waiting": "⏳",
        "canceled": "⏹️",
        "unknown": "❓"
    }
    return status_icons.get(status.lower(), "❓")

def display_workflow_card(workflow):
    """Affiche une carte pour un workflow"""
    with st.container():
        col1, col2, col3, col4 = st.columns([3, 2, 1, 1])

        with col1:
            st.write(f"**{workflow.get('name', 'Sans nom')}**")
            if 'tags' in workflow and workflow['tags']:
                tags_str = ", ".join([tag.get('name', '') for tag in workflow['tags']])
                st.write(f"🏷️ {tags_str}")

        with col2:
            active_status = "🟢 Actif" if workflow.get('active', False) else "🔴 Inactif"
            st.write(active_status)

            if 'updatedAt' in workflow:
                st.write(f"Modifié: {format_datetime(workflow['updatedAt'])}")

        with col3:
            if st.button("▶️", key=f"execute_{workflow['id']}", help="Exécuter le workflow"):
                execute_workflow(workflow['id'], workflow.get('name', 'Sans nom'))

        with col4:
            if st.button("ℹ️", key=f"info_{workflow['id']}", help="Détails du workflow"):
                st.session_state[f"show_workflow_info_{workflow['id']}"] = True

def display_workflow_info(workflow_id):
    """Affiche les informations détaillées d'un workflow"""
    if st.session_state.get(f"show_workflow_info_{workflow_id}", False):
        workflow_details = n8n_api.get_workflow(workflow_id)

        if workflow_details:
            with st.expander(f"Détails du workflow - {workflow_details.get('name', 'Sans nom')}", expanded=True):
                col1, col2 = st.columns(2)

                with col1:
                    st.write("**Informations générales:**")
                    st.write(f"• ID: {workflow_details.get('id', 'N/A')}")
                    st.write(f"• Nom: {workflow_details.get('name', 'Sans nom')}")
                    st.write(f"• Actif: {'Oui' if workflow_details.get('active', False) else 'Non'}")

                    if 'createdAt' in workflow_details:
                        st.write(f"• Créé: {format_datetime(workflow_details['createdAt'])}")
                    if 'updatedAt' in workflow_details:
                        st.write(f"• Modifié: {format_datetime(workflow_details['updatedAt'])}")

                with col2:
                    st.write("**Configuration:**")
                    if 'nodes' in workflow_details:
                        st.write(f"• Nombre de nœuds: {len(workflow_details['nodes'])}")
                    if 'connections' in workflow_details:
                        st.write(f"• Connexions: {len(workflow_details['connections'])}")
                    if 'settings' in workflow_details:
                        settings = workflow_details['settings']
                        if 'timezone' in settings:
                            st.write(f"• Fuseau horaire: {settings['timezone']}")

                # Affichage des nœuds
                if 'nodes' in workflow_details and workflow_details['nodes']:
                    st.write("**Nœuds du workflow:**")
                    for node in workflow_details['nodes']:
                        node_type = node.get('type', 'Inconnu')
                        node_name = node.get('name', 'Sans nom')
                        st.write(f"• {node_name} ({node_type})")

                # Statistiques d'exécution
                stats = n8n_api.get_workflow_statistics(workflow_id)
                if stats:
                    st.write("**Statistiques d'exécution:**")
                    st.write(f"• Exécutions totales: {stats.get('totalExecutions', 0)}")
                    st.write(f"• Succès: {stats.get('successfulExecutions', 0)}")
                    st.write(f"• Erreurs: {stats.get('failedExecutions', 0)}")

                if st.button("Fermer", key=f"close_workflow_info_{workflow_id}"):
                    st.session_state[f"show_workflow_info_{workflow_id}"] = False
                    st.rerun()

def execute_workflow(workflow_id, workflow_name):
    """Exécute un workflow"""
    with st.spinner(f"Exécution de {workflow_name}..."):
        result = n8n_api.execute_workflow(workflow_id)

        if result:
            st.success(f"Workflow {workflow_name} exécuté avec succès!")
            if 'executionId' in result:
                st.info(f"ID d'exécution: {result['executionId']}")
        else:
            st.error(f"Erreur lors de l'exécution de {workflow_name}")

def display_executions_table():
    """Affiche le tableau des exécutions récentes"""
    st.subheader("📊 Exécutions Récentes")

    executions = n8n_api.get_executions(limit=50)

    if executions:
        # Préparation des données pour le tableau
        execution_data = []
        for execution in executions:
            execution_data.append({
                "ID": execution.get('id', 'N/A'),
                "Workflow": execution.get('workflowData', {}).get('name', 'Inconnu'),
                "Statut": execution.get('finished', False),
                "Début": format_datetime(execution.get('startedAt', '')),
                "Fin": format_datetime(execution.get('stoppedAt', '')) if execution.get('stoppedAt') else 'En cours',
                "Mode": execution.get('mode', 'N/A')
            })

        # Affichage du tableau
        df = pd.DataFrame(execution_data)

        # Configuration des colonnes
        column_config = {
            "ID": st.column_config.TextColumn("ID", width="small"),
            "Workflow": st.column_config.TextColumn("Workflow", width="medium"),
            "Statut": st.column_config.TextColumn("Statut", width="small"),
            "Début": st.column_config.TextColumn("Début", width="medium"),
            "Fin": st.column_config.TextColumn("Fin", width="medium"),
            "Mode": st.column_config.TextColumn("Mode", width="small")
        }

        st.dataframe(
            df,
            column_config=column_config,
            use_container_width=True,
            hide_index=True
        )

        # Statistiques rapides
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            total_executions = len(executions)
            st.metric("Total exécutions", total_executions)

        with col2:
            successful = sum(1 for ex in executions if ex.get('finished', False))
            st.metric("Succès", successful)

        with col3:
            failed = total_executions - successful
            st.metric("Échecs", failed)

        with col4:
            success_rate = (successful / total_executions * 100) if total_executions > 0 else 0
            st.metric("Taux de succès", f"{success_rate:.1f}%")

    else:
        st.info("Aucune exécution trouvée")

def display_execution_chart():
    """Affiche un graphique des exécutions par jour"""
    st.subheader("📈 Graphique des Exécutions")

    executions = n8n_api.get_executions(limit=100)

    if executions:
        # Préparation des données
        execution_dates = []
        for execution in executions:
            if 'startedAt' in execution:
                try:
                    dt = datetime.fromisoformat(execution['startedAt'].replace('Z', '+00:00'))
                    execution_dates.append(dt.date())
                except:
                    continue

        if execution_dates:
            # Comptage par jour
            date_counts = {}
            for date in execution_dates:
                date_counts[date] = date_counts.get(date, 0) + 1

            # Création du graphique
            dates = list(date_counts.keys())
            counts = list(date_counts.values())

            fig = go.Figure(data=go.Scatter(
                x=dates,
                y=counts,
                mode='lines+markers',
                name='Exécutions',
                line=dict(color=CHART_COLORS["primary"])
            ))

            fig.update_layout(
                title="Nombre d'exécutions par jour",
                xaxis_title="Date",
                yaxis_title="Nombre d'exécutions",
                height=400
            )

            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("Pas assez de données pour afficher le graphique")
    else:
        st.info("Aucune donnée d'exécution disponible")

def display_workflow_status_chart():
    """Affiche un graphique du statut des workflows"""
    st.subheader("📊 Statut des Workflows")

    workflows = n8n_api.get_workflows()

    if workflows:
        # Comptage par statut
        active_count = sum(1 for wf in workflows if wf.get('active', False))
        inactive_count = len(workflows) - active_count

        # Création du graphique en secteurs
        fig = go.Figure(data=[go.Pie(
            labels=['Actifs', 'Inactifs'],
            values=[active_count, inactive_count],
            hole=0.3,
            marker=dict(colors=[CHART_COLORS["success"], CHART_COLORS["error"]])
        )])

        fig.update_layout(
            title="Répartition des workflows par statut",
            height=400
        )

        st.plotly_chart(fig, use_container_width=True)

        # Métriques
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("Total workflows", len(workflows))

        with col2:
            st.metric("Workflows actifs", active_count)

        with col3:
            st.metric("Workflows inactifs", inactive_count)

    else:
        st.info("Aucun workflow trouvé")

def main():
    """Fonction principale de la page"""

    st.title("⚙️ Gestion des Workflows n8n")

    # Vérification de la disponibilité de n8n
    if not n8n_api.is_available():
        st.error(DEFAULT_MESSAGES["n8n_not_available"])
        st.stop()

    # Récupération des workflows
    workflows = n8n_api.get_workflows()

    # Onglets
    tab1, tab2, tab3 = st.tabs(["📋 Workflows", "📊 Exécutions", "📈 Statistiques"])

    with tab1:
        st.subheader(f"📋 Liste des Workflows ({len(workflows)})")

        if workflows:
            # Filtres
            col1, col2 = st.columns([2, 1])

            with col1:
                search_term = st.text_input(
                    "🔍 Rechercher un workflow:",
                    placeholder="Nom du workflow...",
                    help="Filtrer les workflows par nom"
                )

            with col2:
                status_filter = st.selectbox(
                    "Filtrer par statut:",
                    ["Tous", "Actifs", "Inactifs"]
                )

            # Application des filtres
            filtered_workflows = workflows

            if search_term:
                filtered_workflows = [
                    wf for wf in filtered_workflows
                    if search_term.lower() in wf.get('name', '').lower()
                ]

            if status_filter == "Actifs":
                filtered_workflows = [wf for wf in filtered_workflows if wf.get('active', False)]
            elif status_filter == "Inactifs":
                filtered_workflows = [wf for wf in filtered_workflows if not wf.get('active', False)]

            st.write(f"**{len(filtered_workflows)} workflow(s) trouvé(s)**")

            # Affichage des workflows
            for workflow in filtered_workflows:
                display_workflow_card(workflow)
                display_workflow_info(workflow['id'])
                st.divider()

        else:
            st.info("Aucun workflow trouvé")

    with tab2:
        display_executions_table()
        st.divider()
        display_execution_chart()

    with tab3:
        display_workflow_status_chart()

        # Statistiques globales
        st.divider()
        st.subheader("📊 Statistiques Globales")

        if workflows:
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                total_workflows = len(workflows)
                st.metric("Total workflows", total_workflows)

            with col2:
                active_workflows = sum(1 for wf in workflows if wf.get('active', False))
                st.metric("Workflows actifs", active_workflows)

            with col3:
                # Calcul de la moyenne des nœuds par workflow
                total_nodes = 0
                workflows_with_nodes = 0
                for wf in workflows:
                    workflow_details = n8n_api.get_workflow(wf['id'])
                    if workflow_details and 'nodes' in workflow_details:
                        total_nodes += len(workflow_details['nodes'])
                        workflows_with_nodes += 1

                avg_nodes = total_nodes / workflows_with_nodes if workflows_with_nodes > 0 else 0
                st.metric("Nœuds moyens/workflow", f"{avg_nodes:.1f}")

            with col4:
                # Récupération des exécutions récentes pour calculer le taux de succès
                recent_executions = n8n_api.get_executions(limit=100)
                if recent_executions:
                    successful = sum(1 for ex in recent_executions if ex.get('finished', False))
                    success_rate = (successful / len(recent_executions) * 100)
                    st.metric("Taux de succès", f"{success_rate:.1f}%")
                else:
                    st.metric("Taux de succès", "N/A")

if __name__ == "__main__":
    main()