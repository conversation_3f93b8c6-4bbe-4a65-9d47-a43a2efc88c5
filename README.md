# 🚀 Dashboard Streamlit All-in-One pour Ollama et n8n

Un dashboard complet et interactif pour gérer vos modèles Ollama, workflows n8n, et surveiller votre système en temps réel.

## 🎯 Fonctionnalités

### 🤖 Gestion des Modèles Ollama
- ✅ Téléchargement et suppression de modèles
- ✅ Monitoring des performances et métriques
- ✅ Informations détaillées sur chaque modèle
- ✅ Visualisation de l'utilisation de l'espace disque

### 💬 Interface de Chat
- ✅ Chat en temps réel avec streaming
- ✅ Support de multiples modèles
- ✅ Historique des conversations
- ✅ Paramètres avancés (température, tokens max)
- ✅ Presets de personnalité
- ✅ Export des conversations

### ⚙️ Gestion des Workflows n8n
- ✅ Liste et statut des workflows
- ✅ Déclenchement manuel avec données JSON
- ✅ Monitoring des exécutions en temps réel
- ✅ Activation/désactivation des workflows
- ✅ Statistiques et analyses
- ✅ Logs détaillés

### 📊 Monitoring Système
- ✅ Surveillance CPU, mémoire, disque en temps réel
- ✅ Graphiques interactifs avec Plotly
- ✅ Alertes configurables avec seuils
- ✅ Monitoring des processus
- ✅ Statut des services Ollama et n8n
- ✅ Export des métriques et rapports

## 🛠️ Installation

### Prérequis
- Python 3.8+
- Ollama installé et en fonctionnement (port 11434)
- n8n installé et configuré (port 5678)

### Installation des dépendances
```bash
pip install -r requirements.txt
```

### Structure du projet
```
dashboard_app/
├── app.py                 # Point d'entrée principal
├── config.py              # Configuration centralisée
├── requirements.txt       # Dépendances Python
├── pages/                 # Pages Streamlit
│   ├── 01_🤖_Models.py   # Gestion modèles Ollama
│   ├── 02_💬_Chat.py     # Interface de chat
│   ├── 03_⚙️_Workflows.py # Gestion workflows n8n
│   └── 04_📊_Monitor.py   # Monitoring système
└── utils/                 # Modules utilitaires
    ├── ollama_api.py      # Wrapper API Ollama
    ├── n8n_api.py         # Wrapper API n8n
    └── monitoring.py      # Module de monitoring
```

## 🚀 Lancement de l'Application

### Démarrage simple
```bash
cd dashboard_app
streamlit run app.py
```

### Avec configuration personnalisée
```bash
streamlit run app.py --server.port 8501 --server.address 0.0.0.0
```

L'application sera accessible à l'adresse : `http://localhost:8501`

## ⚙️ Configuration

### Variables de configuration dans `config.py`

```python
# URLs des services
OLLAMA_API_URL = "http://localhost:11434"
N8N_API_URL = "http://localhost:5678"

# Seuils d'alerte
CPU_THRESHOLD = 80        # Pourcentage
MEMORY_THRESHOLD = 85     # Pourcentage

# Paramètres de chat
CHAT_MAX_TOKENS = 4000
CHAT_TEMPERATURE = 0.7
```

### Personnalisation
- Modifiez `config.py` pour adapter les URLs et seuils
- Ajustez les intervalles de monitoring
- Configurez les messages par défaut

## 📱 Utilisation

### 1. Page d'Accueil
- Vue d'ensemble du système
- Statut des services
- Métriques rapides
- Navigation vers les autres pages

### 2. Gestion des Modèles
- Téléchargez des modèles populaires
- Surveillez l'utilisation des ressources
- Gérez l'espace disque
- Consultez les détails techniques

### 3. Interface de Chat
- Sélectionnez votre modèle préféré
- Configurez les paramètres avancés
- Utilisez les presets de personnalité
- Exportez vos conversations

### 4. Workflows n8n
- Visualisez vos workflows
- Déclenchez des exécutions manuelles
- Surveillez les performances
- Analysez les statistiques

### 5. Monitoring Système
- Surveillez les ressources en temps réel
- Configurez des alertes
- Exportez les métriques
- Générez des rapports

## 🔧 Résolution de Problèmes

### Ollama non détecté
- Vérifiez qu'Ollama fonctionne : `ollama list`
- Contrôlez le port 11434 : `curl http://localhost:11434/api/tags`

### n8n non accessible
- Vérifiez le statut de n8n
- Contrôlez le port 5678 : `curl http://localhost:5678/api/v1/workflows`

### Erreurs de dépendances
- Réinstallez les dépendances : `pip install -r requirements.txt --force-reinstall`
- Utilisez un environnement virtuel Python

### Problèmes de performance
- Ajustez l'intervalle de monitoring
- Réduisez l'historique des métriques
- Limitez le nombre de processus surveillés

## 🎨 Personnalisation

### Ajouter de nouveaux modèles
Modifiez la liste dans `pages/01_🤖_Models.py` :
```python
popular_models = [
    "llama3.2:latest",
    "votre-modele:latest"
]
```

### Modifier les seuils d'alerte
Dans `config.py` :
```python
CPU_THRESHOLD = 90      # Nouveau seuil CPU
MEMORY_THRESHOLD = 95   # Nouveau seuil mémoire
```

### Ajouter des presets de chat
Dans `pages/02_💬_Chat.py` :
```python
presets = {
    "Votre Preset": "Instructions personnalisées..."
}
```

## 📊 Métriques et Monitoring

### Métriques collectées
- Utilisation CPU (%)
- Utilisation mémoire (%)
- Utilisation disque (%)
- Trafic réseau (KB/s)
- Nombre de processus
- Statut des services

### Alertes configurables
- Seuils personnalisables
- Niveaux de gravité (bas, moyen, élevé)
- Historique des alertes
- Notifications visuelles

### Export des données
- Format CSV pour les métriques
- Rapports système en Markdown
- Conversations de chat
- Logs des workflows

## 🤝 Contribution

Les contributions sont les bienvenues ! Pour contribuer :

1. Forkez le projet
2. Créez une branche pour votre fonctionnalité
3. Commitez vos changements
4. Poussez vers la branche
5. Ouvrez une Pull Request

## 📝 Licence

Ce projet est sous licence MIT. Voir le fichier LICENSE pour plus de détails.

## 🙏 Remerciements

- Équipe Ollama pour l'API excellente
- Équipe n8n pour les workflows automatisés
- Streamlit pour le framework web
- Plotly pour les visualisations interactives

---

**Développé avec ❤️ pour la communauté open source**
