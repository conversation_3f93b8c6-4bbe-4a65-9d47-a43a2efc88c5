# Créer le fichier requirements.txt
requirements_content = """streamlit>=1.28.0
requests>=2.31.0
pandas>=2.0.0
plotly>=5.15.0
psutil>=5.9.0
websockets>=11.0
aiohttp>=3.8.0
asyncio-mqtt>=0.11.1
numpy>=1.24.0
time
json
"""

with open("dashboard_app/requirements.txt", "w") as f:
    f.write(requirements_content)

print("✅ Fichier requirements.txt créé")

# Créer le fichier de configuration
config_content = '''"""
Configuration centralisée pour le Dashboard Ollama/n8n
"""
import os

# Configuration Ollama
OLLAMA_API_URL = "http://localhost:11434"
OLLAMA_MODELS_ENDPOINT = "/api/tags"
OLLAMA_CHAT_ENDPOINT = "/api/chat"
OLLAMA_GENERATE_ENDPOINT = "/api/generate"
OLLAMA_SHOW_ENDPOINT = "/api/show"
OLLAMA_PULL_ENDPOINT = "/api/pull"
OLLAMA_DELETE_ENDPOINT = "/api/delete"

# Configuration n8n
N8N_API_URL = "http://localhost:5678"
N8N_WORKFLOWS_ENDPOINT = "/api/v1/workflows"
N8N_EXECUTIONS_ENDPOINT = "/api/v1/executions"
N8N_ACTIVE_ENDPOINT = "/api/v1/executions/active"

# Configuration de l'application
APP_TITLE = "🚀 Dashboard Ollama & n8n All-in-One"
APP_ICON = "🤖"
APP_LAYOUT = "wide"

# Paramètres de monitoring
MONITORING_INTERVAL = 2  # secondes
CPU_THRESHOLD = 80  # pourcentage
MEMORY_THRESHOLD = 85  # pourcentage

# Paramètres de chat
CHAT_MAX_TOKENS = 4000
CHAT_TEMPERATURE = 0.7
CHAT_STREAM = True

# Messages par défaut
DEFAULT_ASSISTANT_MESSAGE = "Bonjour ! Je suis votre assistant IA local. Comment puis-je vous aider ?"
ERROR_MESSAGES = {
    "ollama_connection": "❌ Impossible de se connecter à Ollama. Assurez-vous qu'il est en marche sur le port 11434.",
    "n8n_connection": "❌ Impossible de se connecter à n8n. Vérifiez qu'il fonctionne sur le port 5678.",
    "model_not_found": "❌ Modèle non trouvé. Veuillez télécharger le modèle d'abord.",
    "workflow_error": "❌ Erreur lors de l'exécution du workflow."
}

# Couleurs et styles
COLORS = {
    "primary": "#FF6B6B",
    "secondary": "#4ECDC4", 
    "success": "#45B7D1",
    "warning": "#FFA07A",
    "error": "#FF6B6B"
}
'''

with open("dashboard_app/config.py", "w", encoding='utf-8') as f:
    f.write(config_content)

print("✅ Fichier config.py créé")