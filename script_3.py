# Créer le wrapper API n8n
n8n_api_content = '''"""
Wrapper pour l'API n8n
Gestion des workflows et monitoring des exécutions
"""
import requests
import json
from typing import List, Dict, Any, Optional
import streamlit as st
from datetime import datetime
from config import *

class N8nAPI:
    def __init__(self):
        self.base_url = N8N_API_URL
        self.session = requests.Session()
        # Headers par défaut pour n8n
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def is_connected(self) -> bool:
        """Vérifie si n8n est accessible"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/workflows", timeout=5)
            return response.status_code in [200, 401]  # 401 peut indiquer qu'une auth est requise
        except:
            return False
    
    def get_workflows(self) -> List[Dict[str, Any]]:
        """Récupère la liste des workflows"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/workflows")
            if response.status_code == 200:
                return response.json().get("data", [])
            return []
        except Exception as e:
            st.error(f"Erreur lors de la récupération des workflows: {e}")
            return []
    
    def get_workflow_details(self, workflow_id: str) -> Dict[str, Any]:
        """Récupère les détails d'un workflow spécifique"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/workflows/{workflow_id}")
            if response.status_code == 200:
                return response.json()
            return {}
        except Exception as e:
            st.error(f"Erreur lors de la récupération du workflow: {e}")
            return {}
    
    def trigger_workflow(self, workflow_id: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Déclenche l'exécution d'un workflow"""
        try:
            payload = data or {}
            response = self.session.post(
                f"{self.base_url}/api/v1/workflows/{workflow_id}/execute",
                json=payload
            )
            if response.status_code == 200:
                return response.json()
            return {"error": f"Status code: {response.status_code}"}
        except Exception as e:
            st.error(f"Erreur lors du déclenchement: {e}")
            return {"error": str(e)}
    
    def get_executions(self, workflow_id: str = None, limit: int = 20) -> List[Dict[str, Any]]:
        """Récupère les exécutions récentes"""
        try:
            params = {"limit": limit}
            if workflow_id:
                params["workflowId"] = workflow_id
                
            response = self.session.get(
                f"{self.base_url}/api/v1/executions",
                params=params
            )
            if response.status_code == 200:
                return response.json().get("data", [])
            return []
        except Exception as e:
            st.error(f"Erreur lors de la récupération des exécutions: {e}")
            return []
    
    def get_active_executions(self) -> List[Dict[str, Any]]:
        """Récupère les exécutions en cours"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/executions/active")
            if response.status_code == 200:
                return response.json().get("data", [])
            return []
        except Exception as e:
            st.error(f"Erreur lors de la récupération des exécutions actives: {e}")
            return []
    
    def get_execution_details(self, execution_id: str) -> Dict[str, Any]:
        """Récupère les détails d'une exécution"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/executions/{execution_id}")
            if response.status_code == 200:
                return response.json()
            return {}
        except Exception as e:
            st.error(f"Erreur lors de la récupération de l'exécution: {e}")
            return {}
    
    def get_workflow_statistics(self) -> Dict[str, Any]:
        """Récupère les statistiques des workflows"""
        try:
            workflows = self.get_workflows()
            executions = self.get_executions(limit=100)
            
            total_workflows = len(workflows)
            active_workflows = len([w for w in workflows if w.get("active", False)])
            
            # Analyse des exécutions
            successful_executions = len([e for e in executions if e.get("finished", False) and not e.get("error")])
            failed_executions = len([e for e in executions if e.get("error")])
            
            return {
                "total_workflows": total_workflows,
                "active_workflows": active_workflows,
                "inactive_workflows": total_workflows - active_workflows,
                "total_executions": len(executions),
                "successful_executions": successful_executions,
                "failed_executions": failed_executions,
                "success_rate": (successful_executions / len(executions) * 100) if executions else 0
            }
        except Exception as e:
            st.error(f"Erreur lors du calcul des statistiques: {e}")
            return {}
    
    def activate_workflow(self, workflow_id: str) -> bool:
        """Active un workflow"""
        try:
            response = self.session.patch(
                f"{self.base_url}/api/v1/workflows/{workflow_id}",
                json={"active": True}
            )
            return response.status_code == 200
        except Exception as e:
            st.error(f"Erreur lors de l'activation: {e}")
            return False
    
    def deactivate_workflow(self, workflow_id: str) -> bool:
        """Désactive un workflow"""
        try:
            response = self.session.patch(
                f"{self.base_url}/api/v1/workflows/{workflow_id}",
                json={"active": False}
            )
            return response.status_code == 200
        except Exception as e:
            st.error(f"Erreur lors de la désactivation: {e}")
            return False
    
    def get_workflow_logs(self, workflow_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Récupère les logs d'un workflow"""
        executions = self.get_executions(workflow_id, limit)
        logs = []
        
        for execution in executions:
            log_entry = {
                "execution_id": execution.get("id"),
                "status": "success" if execution.get("finished") and not execution.get("error") else "failed",
                "start_time": execution.get("startedAt"),
                "end_time": execution.get("stoppedAt"),
                "duration": self._calculate_duration(execution.get("startedAt"), execution.get("stoppedAt")),
                "error": execution.get("error", {}).get("message") if execution.get("error") else None
            }
            logs.append(log_entry)
        
        return logs
    
    def _calculate_duration(self, start_time: str, end_time: str) -> float:
        """Calcule la durée d'exécution en secondes"""
        try:
            if start_time and end_time:
                start = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                end = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                return (end - start).total_seconds()
        except:
            pass
        return 0.0
'''

with open("dashboard_app/utils/n8n_api.py", "w", encoding='utf-8') as f:
    f.write(n8n_api_content)

print("✅ Wrapper n8n API créé")