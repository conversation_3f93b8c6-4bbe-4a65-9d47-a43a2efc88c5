# Créer la page de chat avec interface streaming
chat_page_content = '''"""
Interface de chat avec streaming pour les modèles Ollama
Support streaming, historique des conversations et paramètres avancés
"""
import streamlit as st
import asyncio
import json
from datetime import datetime
import sys
import os

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from config import *
from utils.ollama_api import OllamaAPI

st.set_page_config(
    page_title="Interface Chat Ollama",
    page_icon="💬",
    layout="wide"
)

# CSS personnalisé pour le chat
st.markdown("""
<style>
    .chat-container {
        max-height: 600px;
        overflow-y: auto;
        padding: 1rem;
        background-color: #f8f9fa;
        border-radius: 10px;
        margin-bottom: 1rem;
    }
    
    .user-message {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 0.8rem 1.2rem;
        border-radius: 18px 18px 4px 18px;
        margin: 0.5rem 0;
        margin-left: 20%;
        text-align: right;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    .assistant-message {
        background: #ffffff;
        color: #333;
        padding: 0.8rem 1.2rem;
        border-radius: 18px 18px 18px 4px;
        margin: 0.5rem 0;
        margin-right: 20%;
        border: 1px solid #e0e0e0;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    .timestamp {
        font-size: 0.7rem;
        opacity: 0.7;
        margin-top: 0.3rem;
    }
    
    .model-selector {
        background: linear-gradient(135deg, #FF6B6B, #4ECDC4);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 1rem;
    }
    
    .chat-stats {
        background: #f1f3f4;
        padding: 0.5rem;
        border-radius: 8px;
        margin: 0.5rem 0;
        text-align: center;
        font-size: 0.8rem;
        color: #666;
    }
    
    .typing-indicator {
        background: #e9ecef;
        padding: 0.8rem 1.2rem;
        border-radius: 18px;
        margin: 0.5rem 0;
        margin-right: 20%;
        animation: pulse 1.5s ease-in-out infinite;
    }
    
    @keyframes pulse {
        0% { opacity: 0.6; }
        50% { opacity: 1; }
        100% { opacity: 0.6; }
    }
    
    .parameter-card {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #4ECDC4;
        margin: 0.5rem 0;
    }
</style>
""", unsafe_allow_html=True)

def init_session_state():
    """Initialise les variables de session pour le chat"""
    if 'ollama_api' not in st.session_state:
        st.session_state.ollama_api = OllamaAPI()
    
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []
    
    if 'selected_model' not in st.session_state:
        st.session_state.selected_model = None
    
    if 'chat_parameters' not in st.session_state:
        st.session_state.chat_parameters = {
            'temperature': CHAT_TEMPERATURE,
            'max_tokens': CHAT_MAX_TOKENS,
            'system_message': ''
        }
    
    if 'conversation_id' not in st.session_state:
        st.session_state.conversation_id = None
    
    if 'typing' not in st.session_state:
        st.session_state.typing = False

def display_model_selector():
    """Affiche le sélecteur de modèle"""
    models = st.session_state.ollama_api.get_models()
    
    if not models:
        st.error("Aucun modèle disponible. Veuillez d'abord télécharger un modèle.")
        return False
    
    model_names = [model['name'] for model in models]
    
    col1, col2 = st.columns([3, 1])
    
    with col1:
        selected = st.selectbox(
            "🤖 Sélectionnez un modèle:",
            options=model_names,
            index=model_names.index(st.session_state.selected_model) if st.session_state.selected_model in model_names else 0,
            key="model_selector"
        )
        
        if selected != st.session_state.selected_model:
            st.session_state.selected_model = selected
    
    with col2:
        if st.button("🔄 Actualiser", use_container_width=True):
            st.rerun()
    
    # Afficher les informations du modèle sélectionné
    if st.session_state.selected_model:
        model_info = next((m for m in models if m['name'] == st.session_state.selected_model), None)
        if model_info:
            size_gb = model_info.get('size', 0) / (1024**3)
            st.info(f"**Modèle sélectionné:** {st.session_state.selected_model} ({size_gb:.1f} GB)")
    
    return True

def display_chat_parameters():
    """Affiche les paramètres de chat avancés"""
    with st.expander("⚙️ Paramètres Avancés", expanded=False):
        col1, col2 = st.columns(2)
        
        with col1:
            temperature = st.slider(
                "🌡️ Température (créativité)",
                min_value=0.0,
                max_value=2.0,
                value=st.session_state.chat_parameters['temperature'],
                step=0.1,
                help="Plus élevé = plus créatif, plus bas = plus déterministe"
            )
            
            max_tokens = st.number_input(
                "📝 Tokens Maximum",
                min_value=100,
                max_value=8000,
                value=st.session_state.chat_parameters['max_tokens'],
                step=100,
                help="Nombre maximum de tokens dans la réponse"
            )
        
        with col2:
            system_message = st.text_area(
                "💭 Message Système",
                value=st.session_state.chat_parameters['system_message'],
                height=100,
                help="Instructions pour personnaliser le comportement de l'IA"
            )
        
        # Sauvegarder les paramètres
        st.session_state.chat_parameters = {
            'temperature': temperature,
            'max_tokens': max_tokens,
            'system_message': system_message
        }
        
        # Presets de messages système
        st.subheader("🎭 Presets de Personnalité")
        
        presets = {
            "Assistant Général": "Tu es un assistant IA serviable et informatif.",
            "Expert Technique": "Tu es un expert technique spécialisé en programmation et technologies.",
            "Créatif": "Tu es un assistant créatif qui aide avec l'écriture et les idées innovantes.",
            "Professeur": "Tu es un professeur patient qui explique les concepts complexes simplement.",
            "Analyste": "Tu es un analyste qui fournit des insights détaillés et des analyses approfondies."
        }
        
        cols = st.columns(len(presets))
        for i, (name, prompt) in enumerate(presets.items()):
            with cols[i]:
                if st.button(name, key=f"preset_{i}", use_container_width=True):
                    st.session_state.chat_parameters['system_message'] = prompt
                    st.rerun()

def display_chat_history():
    """Affiche l'historique du chat"""
    if not st.session_state.chat_history:
        st.markdown("""
        <div class="assistant-message">
            <strong>🤖 Assistant:</strong><br>
            Bonjour ! Je suis votre assistant IA local. Comment puis-je vous aider aujourd'hui ?
            <div class="timestamp">""" + datetime.now().strftime("%H:%M") + """</div>
        </div>
        """, unsafe_allow_html=True)
        return
    
    # Afficher tous les messages
    for message in st.session_state.chat_history:
        timestamp = message.get('timestamp', datetime.now().strftime("%H:%M"))
        
        if message['role'] == 'user':
            st.markdown(f"""
            <div class="user-message">
                <strong>👤 Vous:</strong><br>
                {message['content']}
                <div class="timestamp">{timestamp}</div>
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown(f"""
            <div class="assistant-message">
                <strong>🤖 Assistant:</strong><br>
                {message['content']}
                <div class="timestamp">{timestamp}</div>
            </div>
            """, unsafe_allow_html=True)

def add_message_to_history(role: str, content: str):
    """Ajoute un message à l'historique"""
    message = {
        'role': role,
        'content': content,
        'timestamp': datetime.now().strftime("%H:%M:%S")
    }
    st.session_state.chat_history.append(message)

async def generate_streaming_response(prompt: str):
    """Génère une réponse en streaming"""
    if not st.session_state.selected_model:
        return "Veuillez sélectionner un modèle"
    
    # Préparer les messages pour l'API
    messages = []
    
    # Ajouter le message système si présent
    if st.session_state.chat_parameters['system_message']:
        messages.append({
            'role': 'system',
            'content': st.session_state.chat_parameters['system_message']
        })
    
    # Ajouter l'historique (derniers 10 messages pour le contexte)
    recent_history = st.session_state.chat_history[-10:] if len(st.session_state.chat_history) > 10 else st.session_state.chat_history
    for msg in recent_history:
        messages.append({
            'role': msg['role'],
            'content': msg['content']
        })
    
    # Ajouter le message utilisateur actuel
    messages.append({
        'role': 'user',
        'content': prompt
    })
    
    try:
        # Générer la réponse en streaming
        full_response = ""
        async for chunk in st.session_state.ollama_api.chat_stream(
            model=st.session_state.selected_model,
            messages=messages,
            temperature=st.session_state.chat_parameters['temperature']
        ):
            full_response += chunk
            yield chunk
        
        return full_response
    
    except Exception as e:
        error_msg = f"Erreur lors de la génération: {str(e)}"
        yield error_msg
        return error_msg

def display_chat_stats():
    """Affiche les statistiques du chat"""
    if st.session_state.chat_history:
        user_messages = len([m for m in st.session_state.chat_history if m['role'] == 'user'])
        assistant_messages = len([m for m in st.session_state.chat_history if m['role'] == 'assistant'])
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Messages Utilisateur", user_messages)
        
        with col2:
            st.metric("Réponses Assistant", assistant_messages)
        
        with col3:
            st.metric("Total Messages", len(st.session_state.chat_history))

def export_conversation():
    """Exporte la conversation"""
    if not st.session_state.chat_history:
        st.warning("Aucune conversation à exporter")
        return
    
    # Créer le contenu d'export
    export_content = f"# Conversation - {datetime.now().strftime('%Y-%m-%d %H:%M')}\\n\\n"
    export_content += f"**Modèle utilisé:** {st.session_state.selected_model}\\n\\n"
    
    for message in st.session_state.chat_history:
        role = "Utilisateur" if message['role'] == 'user' else "Assistant"
        export_content += f"**{role} ({message['timestamp']}):**\\n"
        export_content += f"{message['content']}\\n\\n"
    
    # Proposer le téléchargement
    st.download_button(
        label="📥 Télécharger la Conversation",
        data=export_content,
        file_name=f"conversation_{datetime.now().strftime('%Y%m%d_%H%M')}.md",
        mime="text/markdown",
        use_container_width=True
    )

def main():
    """Fonction principale de la page de chat"""
    init_session_state()
    
    st.title("💬 Interface Chat Ollama")
    
    # Vérifier la connexion à Ollama
    if not st.session_state.ollama_api.is_connected():
        st.error(ERROR_MESSAGES["ollama_connection"])
        st.stop()
    
    # Sélecteur de modèle
    if not display_model_selector():
        st.stop()
    
    # Paramètres de chat
    display_chat_parameters()
    
    st.markdown("---")
    
    # Zone de chat principale
    col1, col2 = st.columns([3, 1])
    
    with col1:
        st.subheader("💬 Conversation")
        
        # Container pour l'historique du chat
        chat_container = st.container()
        
        with chat_container:
            display_chat_history()
        
        # Zone de saisie
        user_input = st.chat_input("Tapez votre message ici...")
        
        if user_input:
            # Ajouter le message utilisateur
            add_message_to_history('user', user_input)
            
            # Afficher le message utilisateur immédiatement
            st.markdown(f"""
            <div class="user-message">
                <strong>👤 Vous:</strong><br>
                {user_input}
                <div class="timestamp">{datetime.now().strftime("%H:%M:%S")}</div>
            </div>
            """, unsafe_allow_html=True)
            
            # Indicateur de frappe
            with st.spinner("🤖 L'assistant réfléchit..."):
                # Générer la réponse
                try:
                    response = st.session_state.ollama_api.generate_response(
                        model=st.session_state.selected_model,
                        prompt=user_input,
                        stream=False
                    )
                    
                    if response:
                        add_message_to_history('assistant', response)
                        st.rerun()
                    else:
                        st.error("Impossible de générer une réponse")
                
                except Exception as e:
                    st.error(f"Erreur: {e}")
    
    with col2:
        st.subheader("📊 Statistiques")
        display_chat_stats()
        
        st.markdown("---")
        
        # Actions de conversation
        st.subheader("🛠️ Actions")
        
        export_conversation()
        
        if st.button("🗑️ Effacer Historique", use_container_width=True):
            st.session_state.chat_history = []
            st.success("Historique effacé!")
            st.rerun()
        
        if st.button("💾 Nouvelle Conversation", use_container_width=True):
            st.session_state.chat_history = []
            st.session_state.conversation_id = None
            st.success("Nouvelle conversation démarrée!")
            st.rerun()
        
        st.markdown("---")
        
        # Informations du modèle
        if st.session_state.selected_model:
            st.subheader("🤖 Infos Modèle")
            metrics = st.session_state.ollama_api.get_model_metrics(st.session_state.selected_model)
            
            st.metric("Mémoire", f"{metrics['memory_usage']} MB")
            st.metric("Tokens/sec", metrics['tokens_per_second'])
            st.metric("Latence", f"{metrics['response_time']:.1f}s")

if __name__ == "__main__":
    main()
'''

with open("dashboard_app/pages/02_💬_Chat.py", "w", encoding='utf-8') as f:
    f.write(chat_page_content)

print("✅ Page interface de chat créée")