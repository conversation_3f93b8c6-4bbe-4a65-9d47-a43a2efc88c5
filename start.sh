#!/bin/bash

echo "🚀 Lancement du Dashboard Ollama & n8n"
echo "======================================"

# Vérification des prérequis
echo "📋 Vérification des prérequis..."

# Vérifier Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 n'est pas installé"
    exit 1
fi

# Vérifier Ollama
if ! curl -s http://localhost:11434/api/tags > /dev/null; then
    echo "⚠️  Ollama ne semble pas fonctionner sur le port 11434"
    echo "   Assurez-vous qu'Ollama est démarré avec: ollama serve"
fi

# Vérifier n8n
if ! curl -s http://localhost:5678/api/v1/workflows > /dev/null; then
    echo "⚠️  n8n ne semble pas fonctionner sur le port 5678"
    echo "   Assurez-vous que n8n est démarré"
fi

# Installation des dépendances si nécessaire
if [ ! -d "venv" ]; then
    echo "📦 Création de l'environnement virtuel..."
    python3 -m venv venv
fi

echo "📦 Activation de l'environnement virtuel..."
source venv/bin/activate

echo "📦 Installation des dépendances..."
pip install -r requirements.txt > /dev/null 2>&1

echo "🚀 Démarrage de l'application..."
echo "   L'application sera accessible sur: http://localhost:8501"
echo ""

streamlit run app.py
