@echo off
echo 🚀 Lancement du Dashboard Ollama & n8n
echo ======================================

echo 📋 Vérification des prérequis...

REM Vérifier Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python n'est pas installé ou pas dans le PATH
    pause
    exit /b 1
)

REM Créer l'environnement virtuel si nécessaire
if not exist "venv" (
    echo 📦 Création de l'environnement virtuel...
    python -m venv venv
)

echo 📦 Activation de l'environnement virtuel...
call venv\Scripts\activate.bat

echo 📦 Installation des dépendances...
pip install -r requirements.txt >nul 2>&1

echo 🚀 Démarrage de l'application...
echo    L'application sera accessible sur: http://localhost:8501
echo.

streamlit run app.py
