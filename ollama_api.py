"""
Module pour l'interaction avec l'API Ollama
"""

import requests
import json
import time
from typing import Dict, List, Optional, Generator, Any
import streamlit as st
from config import OLLAMA_API_URL, OLLAMA_CONFIG


class OllamaAPI:
    """Wrapper pour l'API Ollama"""

    def __init__(self, base_url: str = OLLAMA_API_URL):
        self.base_url = base_url.rstrip('/')
        self.timeout = OLLAMA_CONFIG["timeout"]
        self.max_retries = OLLAMA_CONFIG["max_retries"]
        self.retry_delay = OLLAMA_CONFIG["retry_delay"]
        self.stream_timeout = OLLAMA_CONFIG["stream_timeout"]

    def _make_request(self, endpoint: str, method: str = "GET", data: Optional[Dict] = None, stream: bool = False) -> requests.Response:
        """Effectue une requête HTTP avec gestion des erreurs et retry"""
        url = f"{self.base_url}{endpoint}"

        for attempt in range(self.max_retries):
            try:
                if method == "GET":
                    response = requests.get(url, timeout=self.timeout, stream=stream)
                elif method == "POST":
                    response = requests.post(url, json=data, timeout=self.timeout, stream=stream)
                elif method == "DELETE":
                    response = requests.delete(url, json=data, timeout=self.timeout)
                else:
                    raise ValueError(f"Méthode HTTP non supportée: {method}")

                response.raise_for_status()
                return response

            except requests.exceptions.RequestException as e:
                if attempt == self.max_retries - 1:
                    raise e
                time.sleep(self.retry_delay)

        raise Exception("Nombre maximum de tentatives atteint")

    def is_available(self) -> bool:
        """Vérifie si Ollama est disponible"""
        try:
            response = self._make_request("/api/tags")
            return response.status_code == 200
        except:
            return False

    def get_models(self) -> List[Dict]:
        """Récupère la liste des modèles installés"""
        try:
            response = self._make_request("/api/tags")
            data = response.json()
            return data.get("models", [])
        except Exception as e:
            st.error(f"Erreur lors de la récupération des modèles: {e}")
            return []

    def get_model_info(self, model_name: str) -> Optional[Dict]:
        """Récupère les informations détaillées d'un modèle"""
        try:
            data = {"name": model_name}
            response = self._make_request("/api/show", method="POST", data=data)
            return response.json()
        except Exception as e:
            st.error(f"Erreur lors de la récupération des infos du modèle {model_name}: {e}")
            return None

    def pull_model(self, model_name: str) -> Generator[Dict, None, None]:
        """Télécharge un modèle avec streaming du progrès"""
        try:
            data = {"name": model_name}
            response = self._make_request("/api/pull", method="POST", data=data, stream=True)

            for line in response.iter_lines():
                if line:
                    try:
                        yield json.loads(line.decode('utf-8'))
                    except json.JSONDecodeError:
                        continue

        except Exception as e:
            yield {"error": f"Erreur lors du téléchargement: {e}"}

    def delete_model(self, model_name: str) -> bool:
        """Supprime un modèle"""
        try:
            data = {"name": model_name}
            response = self._make_request("/api/delete", method="DELETE", data=data)
            return True
        except Exception as e:
            st.error(f"Erreur lors de la suppression du modèle {model_name}: {e}")
            return False

    def generate_chat(self, model: str, messages: List[Dict], **kwargs) -> Generator[Dict, None, None]:
        """Génère une réponse de chat avec streaming"""
        try:
            data = {
                "model": model,
                "messages": messages,
                "stream": True,
                **kwargs
            }

            response = self._make_request("/api/chat", method="POST", data=data, stream=True)

            for line in response.iter_lines():
                if line:
                    try:
                        chunk = json.loads(line.decode('utf-8'))
                        yield chunk
                    except json.JSONDecodeError:
                        continue

        except Exception as e:
            yield {"error": f"Erreur lors de la génération: {e}"}

    def generate_completion(self, model: str, prompt: str, **kwargs) -> Generator[Dict, None, None]:
        """Génère une completion avec streaming"""
        try:
            data = {
                "model": model,
                "prompt": prompt,
                "stream": True,
                **kwargs
            }

            response = self._make_request("/api/generate", method="POST", data=data, stream=True)

            for line in response.iter_lines():
                if line:
                    try:
                        chunk = json.loads(line.decode('utf-8'))
                        yield chunk
                    except json.JSONDecodeError:
                        continue

        except Exception as e:
            yield {"error": f"Erreur lors de la génération: {e}"}

    def get_running_models(self) -> List[Dict]:
        """Récupère la liste des modèles en cours d'exécution"""
        try:
            response = self._make_request("/api/ps")
            data = response.json()
            return data.get("models", [])
        except Exception as e:
            st.error(f"Erreur lors de la récupération des modèles en cours: {e}")
            return []

    def copy_model(self, source: str, destination: str) -> bool:
        """Copie un modèle"""
        try:
            data = {"source": source, "destination": destination}
            response = self._make_request("/api/copy", method="POST", data=data)
            return True
        except Exception as e:
            st.error(f"Erreur lors de la copie du modèle: {e}")
            return False

    def create_model(self, name: str, modelfile: str) -> Generator[Dict, None, None]:
        """Crée un modèle personnalisé"""
        try:
            data = {"name": name, "modelfile": modelfile}
            response = self._make_request("/api/create", method="POST", data=data, stream=True)

            for line in response.iter_lines():
                if line:
                    try:
                        yield json.loads(line.decode('utf-8'))
                    except json.JSONDecodeError:
                        continue

        except Exception as e:
            yield {"error": f"Erreur lors de la création du modèle: {e}"}


# Instance globale
ollama_api = OllamaAPI()