# Créer le wrapper API Ollama
ollama_api_content = '''"""
Wrapper pour l'API Ollama
Gestion des modèles et communication avec le serveur Ollama
"""
import requests
import json
import asyncio
import aiohttp
from typing import List, Dict, Any, Optional, AsyncGenerator
import streamlit as st
from config import *

class OllamaAPI:
    def __init__(self):
        self.base_url = OLLAMA_API_URL
        self.session = requests.Session()
        
    def is_connected(self) -> bool:
        """Vérifie si Ollama est accessible"""
        try:
            response = self.session.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_models(self) -> List[Dict[str, Any]]:
        """Récupère la liste des modèles installés"""
        try:
            response = self.session.get(f"{self.base_url}/api/tags")
            if response.status_code == 200:
                return response.json().get("models", [])
            return []
        except Exception as e:
            st.error(f"Erreur lors de la récupération des modèles: {e}")
            return []
    
    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """Récupère les informations détaillées d'un modèle"""
        try:
            data = {"name": model_name}
            response = self.session.post(
                f"{self.base_url}/api/show", 
                json=data
            )
            if response.status_code == 200:
                return response.json()
            return {}
        except Exception as e:
            st.error(f"Erreur lors de la récupération des infos du modèle: {e}")
            return {}
    
    def pull_model(self, model_name: str) -> bool:
        """Télécharge un modèle"""
        try:
            data = {"name": model_name}
            response = self.session.post(
                f"{self.base_url}/api/pull",
                json=data,
                stream=True
            )
            return response.status_code == 200
        except Exception as e:
            st.error(f"Erreur lors du téléchargement: {e}")
            return False
    
    def delete_model(self, model_name: str) -> bool:
        """Supprime un modèle"""
        try:
            data = {"name": model_name}
            response = self.session.delete(
                f"{self.base_url}/api/delete",
                json=data
            )
            return response.status_code == 200
        except Exception as e:
            st.error(f"Erreur lors de la suppression: {e}")
            return False
    
    def generate_response(self, model: str, prompt: str, stream: bool = True) -> str:
        """Génère une réponse simple"""
        try:
            data = {
                "model": model,
                "prompt": prompt,
                "stream": stream
            }
            response = self.session.post(
                f"{self.base_url}/api/generate",
                json=data,
                stream=stream
            )
            
            if stream:
                full_response = ""
                for line in response.iter_lines():
                    if line:
                        json_response = json.loads(line)
                        if "response" in json_response:
                            full_response += json_response["response"]
                        if json_response.get("done", False):
                            break
                return full_response
            else:
                return response.json().get("response", "")
                
        except Exception as e:
            st.error(f"Erreur lors de la génération: {e}")
            return ""
    
    async def chat_stream(self, model: str, messages: List[Dict], temperature: float = 0.7) -> AsyncGenerator[str, None]:
        """Chat en streaming pour l'interface de chat"""
        try:
            data = {
                "model": model,
                "messages": messages,
                "stream": True,
                "options": {
                    "temperature": temperature
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/api/chat",
                    json=data
                ) as response:
                    async for line in response.content:
                        if line:
                            try:
                                json_response = json.loads(line.decode())
                                if "message" in json_response and "content" in json_response["message"]:
                                    yield json_response["message"]["content"]
                                if json_response.get("done", False):
                                    break
                            except json.JSONDecodeError:
                                continue
                                
        except Exception as e:
            yield f"Erreur: {e}"
    
    def get_running_models(self) -> List[Dict[str, Any]]:
        """Récupère les modèles en cours d'exécution"""
        try:
            response = self.session.get(f"{self.base_url}/api/ps")
            if response.status_code == 200:
                return response.json().get("models", [])
            return []
        except:
            return []
    
    def get_model_metrics(self, model_name: str) -> Dict[str, Any]:
        """Récupère les métriques d'un modèle (simulé pour l'exemple)"""
        # Cette fonction pourrait être étendue pour récupérer de vraies métriques
        import random
        return {
            "memory_usage": random.randint(1000, 8000),  # MB
            "tokens_per_second": random.randint(10, 50),
            "response_time": random.uniform(0.5, 3.0),  # secondes
            "requests_count": random.randint(1, 100)
        }
'''

with open("dashboard_app/utils/ollama_api.py", "w", encoding='utf-8') as f:
    f.write(ollama_api_content)

print("✅ Wrapper Ollama API créé")