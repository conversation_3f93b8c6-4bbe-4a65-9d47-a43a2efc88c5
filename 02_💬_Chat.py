"""
Page d'interface de chat avec Ollama
"""

import streamlit as st
import json
from datetime import datetime
import time
import io

from config import CHAT_CONFIG, CHAT_PRESETS, DEFAULT_MESSAGES, EXPORT_CONFIG
from ollama_api import ollama_api

# Configuration de la page
st.set_page_config(
    page_title="💬 Chat Interface",
    page_icon="💬",
    layout="wide"
)

def initialize_session_state():
    """Initialise les variables de session"""
    if "messages" not in st.session_state:
        st.session_state.messages = []

    if "chat_history" not in st.session_state:
        st.session_state.chat_history = []

    if "current_model" not in st.session_state:
        st.session_state.current_model = None

    if "chat_settings" not in st.session_state:
        st.session_state.chat_settings = CHAT_CONFIG.copy()

def display_sidebar():
    """Affiche la barre latérale avec les paramètres"""
    with st.sidebar:
        st.header("⚙️ Paramètres de Chat")

        # Sélection du modèle
        models = ollama_api.get_models()
        if models:
            model_names = [model['name'] for model in models]
            selected_model = st.selectbox(
                "Modèle:",
                model_names,
                index=0 if model_names else None,
                help="Choisissez le modèle à utiliser pour la conversation"
            )
            st.session_state.current_model = selected_model
        else:
            st.error("Aucun modèle disponible")
            st.session_state.current_model = None

        st.divider()

        # Presets de personnalité
        st.subheader("🎭 Presets de Personnalité")
        selected_preset = st.selectbox(
            "Choisir un preset:",
            list(CHAT_PRESETS.keys()),
            help="Sélectionnez un preset pour définir le comportement de l'IA"
        )

        if st.button("Appliquer le Preset"):
            system_message = {
                "role": "system",
                "content": CHAT_PRESETS[selected_preset]
            }
            # Remplacer ou ajouter le message système
            if st.session_state.messages and st.session_state.messages[0]["role"] == "system":
                st.session_state.messages[0] = system_message
            else:
                st.session_state.messages.insert(0, system_message)
            st.success(f"Preset '{selected_preset}' appliqué!")

        st.divider()

        # Paramètres avancés
        st.subheader("🔧 Paramètres Avancés")

        st.session_state.chat_settings["temperature"] = st.slider(
            "Température:",
            min_value=0.0,
            max_value=2.0,
            value=st.session_state.chat_settings["temperature"],
            step=0.1,
            help="Contrôle la créativité des réponses (0 = déterministe, 2 = très créatif)"
        )

        st.session_state.chat_settings["max_tokens"] = st.slider(
            "Tokens maximum:",
            min_value=100,
            max_value=8000,
            value=st.session_state.chat_settings["max_tokens"],
            step=100,
            help="Nombre maximum de tokens dans la réponse"
        )

        st.session_state.chat_settings["top_p"] = st.slider(
            "Top P:",
            min_value=0.0,
            max_value=1.0,
            value=st.session_state.chat_settings["top_p"],
            step=0.05,
            help="Contrôle la diversité des réponses"
        )

        st.session_state.chat_settings["top_k"] = st.slider(
            "Top K:",
            min_value=1,
            max_value=100,
            value=st.session_state.chat_settings["top_k"],
            step=1,
            help="Limite le nombre de tokens candidats"
        )

        st.session_state.chat_settings["repeat_penalty"] = st.slider(
            "Pénalité de répétition:",
            min_value=0.5,
            max_value=2.0,
            value=st.session_state.chat_settings["repeat_penalty"],
            step=0.1,
            help="Pénalise la répétition de mots"
        )

        st.divider()

        # Actions
        st.subheader("📋 Actions")

        if st.button("🗑️ Effacer la Conversation", type="secondary"):
            st.session_state.messages = []
            st.success("Conversation effacée!")
            st.rerun()

        if st.button("💾 Sauvegarder la Conversation"):
            save_conversation()

        if st.button("📥 Exporter en JSON"):
            export_conversation_json()

        if st.button("📄 Exporter en Markdown"):
            export_conversation_markdown()

def display_message(message, is_user=True):
    """Affiche un message dans le chat"""
    if is_user:
        with st.chat_message("user"):
            st.write(message)
    else:
        with st.chat_message("assistant"):
            st.write(message)

def display_chat_history():
    """Affiche l'historique de la conversation"""
    for message in st.session_state.messages:
        if message["role"] == "system":
            continue  # Ne pas afficher les messages système

        is_user = message["role"] == "user"
        display_message(message["content"], is_user)

def generate_response(user_input):
    """Génère une réponse en streaming"""
    if not st.session_state.current_model:
        st.error("Aucun modèle sélectionné")
        return

    # Ajouter le message utilisateur
    user_message = {"role": "user", "content": user_input}
    st.session_state.messages.append(user_message)

    # Afficher le message utilisateur
    display_message(user_input, is_user=True)

    # Préparer les messages pour l'API
    api_messages = [msg for msg in st.session_state.messages if msg["role"] != "system"]

    # Ajouter le message système au début si présent
    system_messages = [msg for msg in st.session_state.messages if msg["role"] == "system"]
    if system_messages:
        api_messages = system_messages + api_messages

    # Générer la réponse en streaming
    with st.chat_message("assistant"):
        message_placeholder = st.empty()
        full_response = ""

        try:
            for chunk in ollama_api.generate_chat(
                model=st.session_state.current_model,
                messages=api_messages,
                temperature=st.session_state.chat_settings["temperature"],
                max_tokens=st.session_state.chat_settings["max_tokens"],
                top_p=st.session_state.chat_settings["top_p"],
                top_k=st.session_state.chat_settings["top_k"],
                repeat_penalty=st.session_state.chat_settings["repeat_penalty"]
            ):
                if 'error' in chunk:
                    st.error(f"Erreur: {chunk['error']}")
                    break

                if 'message' in chunk and 'content' in chunk['message']:
                    content = chunk['message']['content']
                    full_response += content
                    message_placeholder.markdown(full_response + "▌")

                if chunk.get('done', False):
                    break

            message_placeholder.markdown(full_response)

            # Ajouter la réponse à l'historique
            assistant_message = {"role": "assistant", "content": full_response}
            st.session_state.messages.append(assistant_message)

        except Exception as e:
            st.error(f"Erreur lors de la génération: {e}")

def save_conversation():
    """Sauvegarde la conversation dans l'historique"""
    if st.session_state.messages:
        conversation = {
            "timestamp": datetime.now().isoformat(),
            "model": st.session_state.current_model,
            "messages": st.session_state.messages.copy(),
            "settings": st.session_state.chat_settings.copy()
        }
        st.session_state.chat_history.append(conversation)
        st.success("Conversation sauvegardée!")

def export_conversation_json():
    """Exporte la conversation en JSON"""
    if not st.session_state.messages:
        st.warning("Aucune conversation à exporter")
        return

    conversation_data = {
        "timestamp": datetime.now().isoformat(),
        "model": st.session_state.current_model,
        "messages": st.session_state.messages,
        "settings": st.session_state.chat_settings
    }

    json_str = json.dumps(conversation_data, indent=2, ensure_ascii=False)

    st.download_button(
        label="📥 Télécharger JSON",
        data=json_str,
        file_name=f"conversation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
        mime="application/json"
    )

def export_conversation_markdown():
    """Exporte la conversation en Markdown"""
    if not st.session_state.messages:
        st.warning("Aucune conversation à exporter")
        return

    markdown_content = f"""# Conversation avec {st.session_state.current_model}

**Date:** {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}
**Modèle:** {st.session_state.current_model}

---

"""

    for message in st.session_state.messages:
        if message["role"] == "system":
            markdown_content += f"**Système:** {message['content']}\n\n"
        elif message["role"] == "user":
            markdown_content += f"**Utilisateur:** {message['content']}\n\n"
        elif message["role"] == "assistant":
            markdown_content += f"**Assistant:** {message['content']}\n\n"

    markdown_content += f"""---

**Paramètres utilisés:**
- Température: {st.session_state.chat_settings['temperature']}
- Tokens max: {st.session_state.chat_settings['max_tokens']}
- Top P: {st.session_state.chat_settings['top_p']}
- Top K: {st.session_state.chat_settings['top_k']}
- Pénalité répétition: {st.session_state.chat_settings['repeat_penalty']}
"""

    st.download_button(
        label="📄 Télécharger Markdown",
        data=markdown_content,
        file_name=f"conversation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
        mime="text/markdown"
    )

def display_conversation_history():
    """Affiche l'historique des conversations sauvegardées"""
    if not st.session_state.chat_history:
        st.info("Aucune conversation sauvegardée")
        return

    st.subheader("📚 Historique des Conversations")

    for i, conversation in enumerate(reversed(st.session_state.chat_history)):
        timestamp = datetime.fromisoformat(conversation["timestamp"])

        with st.expander(f"Conversation du {timestamp.strftime('%d/%m/%Y %H:%M')} - {conversation['model']}"):
            col1, col2 = st.columns([3, 1])

            with col1:
                # Afficher les messages
                for message in conversation["messages"]:
                    if message["role"] == "system":
                        continue

                    role_icon = "👤" if message["role"] == "user" else "🤖"
                    st.write(f"{role_icon} **{message['role'].title()}:** {message['content'][:100]}...")

            with col2:
                if st.button("🔄 Restaurer", key=f"restore_{i}"):
                    st.session_state.messages = conversation["messages"].copy()
                    st.session_state.chat_settings = conversation["settings"].copy()
                    st.session_state.current_model = conversation["model"]
                    st.success("Conversation restaurée!")
                    st.rerun()

                if st.button("🗑️ Supprimer", key=f"delete_history_{i}"):
                    st.session_state.chat_history.pop(-(i+1))
                    st.success("Conversation supprimée!")
                    st.rerun()

def main():
    """Fonction principale de la page"""

    # Initialisation
    initialize_session_state()

    st.title("💬 Interface de Chat")

    # Vérification de la disponibilité d'Ollama
    if not ollama_api.is_available():
        st.error(DEFAULT_MESSAGES["ollama_not_available"])
        st.stop()

    # Affichage de la barre latérale
    display_sidebar()

    # Vérification qu'un modèle est sélectionné
    if not st.session_state.current_model:
        st.warning("Veuillez sélectionner un modèle dans la barre latérale")
        st.stop()

    # Onglets
    tab1, tab2 = st.tabs(["💬 Chat", "📚 Historique"])

    with tab1:
        # Affichage de l'historique de la conversation actuelle
        display_chat_history()

        # Zone de saisie
        user_input = st.chat_input("Tapez votre message ici...")

        if user_input:
            generate_response(user_input)

    with tab2:
        display_conversation_history()

if __name__ == "__main__":
    main()