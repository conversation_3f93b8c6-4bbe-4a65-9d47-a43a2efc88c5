"""
Configuration centralisée pour le Dashboard Streamlit All-in-One
"""

import os
from typing import Dict, List

# URLs des services
OLLAMA_API_URL = os.getenv("OLLAMA_API_URL", "http://localhost:11434")
N8N_API_URL = os.getenv("N8N_API_URL", "http://localhost:5678")

# Configuration Streamlit
STREAMLIT_CONFIG = {
    "page_title": "🚀 Dashboard All-in-One",
    "page_icon": "🚀",
    "layout": "wide",
    "initial_sidebar_state": "expanded"
}

# Seuils d'alerte pour le monitoring
CPU_THRESHOLD = int(os.getenv("CPU_THRESHOLD", "80"))        # Pourcentage
MEMORY_THRESHOLD = int(os.getenv("MEMORY_THRESHOLD", "85"))  # Pourcentage
DISK_THRESHOLD = int(os.getenv("DISK_THRESHOLD", "90"))      # Pourcentage
NETWORK_THRESHOLD = int(os.getenv("NETWORK_THRESHOLD", "1000"))  # KB/s

# Paramètres de chat
CHAT_CONFIG = {
    "max_tokens": int(os.getenv("CHAT_MAX_TOKENS", "4000")),
    "temperature": float(os.getenv("CHAT_TEMPERATURE", "0.7")),
    "top_p": float(os.getenv("CHAT_TOP_P", "0.9")),
    "top_k": int(os.getenv("CHAT_TOP_K", "40")),
    "repeat_penalty": float(os.getenv("CHAT_REPEAT_PENALTY", "1.1")),
    "stream": True
}

# Modèles populaires Ollama
POPULAR_MODELS = [
    "llama3.2:latest",
    "llama3.2:3b",
    "llama3.1:8b",
    "llama3.1:70b",
    "mistral:latest",
    "mistral:7b",
    "codellama:latest",
    "codellama:7b",
    "phi3:latest",
    "phi3:mini",
    "gemma2:latest",
    "gemma2:9b",
    "qwen2:latest",
    "qwen2:7b",
    "deepseek-coder:latest",
    "deepseek-coder:6.7b"
]

# Presets de personnalité pour le chat
CHAT_PRESETS = {
    "Assistant Général": "Tu es un assistant IA serviable, précis et bienveillant. Réponds de manière claire et concise.",
    "Expert Technique": "Tu es un expert technique spécialisé en développement logiciel et infrastructure. Fournis des réponses détaillées et techniques.",
    "Créatif": "Tu es un assistant créatif qui aide à générer des idées innovantes et originales. Sois imaginatif et inspirant.",
    "Analyste": "Tu es un analyste méticuleux qui examine les problèmes sous tous les angles. Fournis des analyses approfondies et structurées.",
    "Enseignant": "Tu es un enseignant patient qui explique les concepts complexes de manière simple et pédagogique.",
    "Développeur": "Tu es un développeur expérimenté qui aide avec le code, l'architecture et les bonnes pratiques de développement."
}

# Configuration du monitoring
MONITORING_CONFIG = {
    "refresh_interval": int(os.getenv("MONITORING_REFRESH_INTERVAL", "5")),  # secondes
    "history_length": int(os.getenv("MONITORING_HISTORY_LENGTH", "100")),   # nombre de points
    "chart_height": int(os.getenv("MONITORING_CHART_HEIGHT", "400")),       # pixels
    "enable_alerts": os.getenv("MONITORING_ENABLE_ALERTS", "true").lower() == "true"
}

# Configuration n8n
N8N_CONFIG = {
    "timeout": int(os.getenv("N8N_TIMEOUT", "30")),  # secondes
    "max_retries": int(os.getenv("N8N_MAX_RETRIES", "3")),
    "retry_delay": int(os.getenv("N8N_RETRY_DELAY", "1"))  # secondes
}

# Configuration Ollama
OLLAMA_CONFIG = {
    "timeout": int(os.getenv("OLLAMA_TIMEOUT", "60")),  # secondes
    "max_retries": int(os.getenv("OLLAMA_MAX_RETRIES", "3")),
    "retry_delay": int(os.getenv("OLLAMA_RETRY_DELAY", "2")),  # secondes
    "stream_timeout": int(os.getenv("OLLAMA_STREAM_TIMEOUT", "300"))  # secondes
}

# Messages par défaut
DEFAULT_MESSAGES = {
    "welcome": "👋 Bienvenue dans le Dashboard All-in-One pour Ollama et n8n !",
    "ollama_not_available": "⚠️ Ollama n'est pas accessible. Vérifiez que le service est démarré.",
    "n8n_not_available": "⚠️ n8n n'est pas accessible. Vérifiez que le service est démarré.",
    "no_models": "ℹ️ Aucun modèle Ollama installé. Téléchargez-en un pour commencer.",
    "no_workflows": "ℹ️ Aucun workflow n8n trouvé. Créez-en un dans n8n pour commencer."
}

# Couleurs pour les graphiques
CHART_COLORS = {
    "primary": "#1f77b4",
    "secondary": "#ff7f0e",
    "success": "#2ca02c",
    "warning": "#ff7f0e",
    "danger": "#d62728",
    "info": "#17a2b8",
    "light": "#f8f9fa",
    "dark": "#343a40"
}

# Configuration des exports
EXPORT_CONFIG = {
    "csv_separator": ",",
    "date_format": "%Y-%m-%d %H:%M:%S",
    "encoding": "utf-8"
}

# Niveaux de log
LOG_LEVELS = {
    "DEBUG": 10,
    "INFO": 20,
    "WARNING": 30,
    "ERROR": 40,
    "CRITICAL": 50
}

# Configuration des alertes
ALERT_CONFIG = {
    "levels": {
        "low": {"color": "yellow", "icon": "⚠️"},
        "medium": {"color": "orange", "icon": "🔶"},
        "high": {"color": "red", "icon": "🚨"}
    },
    "max_alerts": int(os.getenv("MAX_ALERTS", "50")),
    "alert_duration": int(os.getenv("ALERT_DURATION", "300"))  # secondes
}