"""
Script de démarrage pour le Dashboard Ollama/n8n
"""

import subprocess
import sys
import os
from pathlib import Path

def check_python():
    """Vérifie que Python est installé"""
    try:
        result = subprocess.run([sys.executable, "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Python détecté: {result.stdout.strip()}")
            return True
        else:
            print("❌ Python non trouvé")
            return False
    except Exception as e:
        print(f"❌ Erreur lors de la vérification de Python: {e}")
        return False

def install_dependencies():
    """Installe les dépendances"""
    print("📦 Installation des dépendances...")
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Dépendances installées avec succès")
            return True
        else:
            print(f"❌ Erreur lors de l'installation: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Erreur lors de l'installation des dépendances: {e}")
        return False

def check_files():
    """Vérifie que tous les fichiers nécessaires sont présents"""
    required_files = [
        "app.py",
        "config.py", 
        "ollama_api.py",
        "n8n_api.py",
        "monitoring.py",
        "requirements.txt",
        "01_🤖_Models.py",
        "02_💬_Chat.py", 
        "03_⚙️_Workflows.py",
        "04_📊_Monitor.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Fichiers manquants: {', '.join(missing_files)}")
        return False
    else:
        print("✅ Tous les fichiers requis sont présents")
        return True

def start_streamlit():
    """Démarre l'application Streamlit"""
    print("🚀 Démarrage de l'application Dashboard...")
    
    try:
        # Démarrage de Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ])
    except KeyboardInterrupt:
        print("\n⏹️ Application arrêtée par l'utilisateur")
    except Exception as e:
        print(f"❌ Erreur lors du démarrage: {e}")

def main():
    """Fonction principale"""
    print("🚀 Dashboard Streamlit All-in-One pour Ollama et n8n")
    print("=" * 60)
    
    # Vérifications préliminaires
    if not check_python():
        print("\n❌ Python n'est pas installé ou accessible.")
        print("Veuillez installer Python 3.8+ depuis https://python.org")
        return False
    
    if not check_files():
        print("\n❌ Fichiers manquants. Vérifiez l'installation.")
        return False
    
    # Installation des dépendances
    print("\n📋 Vérification des dépendances...")
    try:
        import streamlit
        print("✅ Streamlit déjà installé")
    except ImportError:
        if not install_dependencies():
            print("\n❌ Impossible d'installer les dépendances.")
            print("Essayez manuellement: pip install -r requirements.txt")
            return False
    
    # Informations de démarrage
    print("\n" + "=" * 60)
    print("📋 INFORMATIONS DE DÉMARRAGE")
    print("=" * 60)
    print("🌐 URL de l'application: http://localhost:8501")
    print("📚 Pages disponibles:")
    print("   • 🏠 Accueil - Vue d'ensemble du système")
    print("   • 🤖 Modèles - Gestion des modèles Ollama")
    print("   • 💬 Chat - Interface de conversation")
    print("   • ⚙️ Workflows - Gestion des workflows n8n")
    print("   • 📊 Monitor - Surveillance système")
    print("\n🔧 Configuration requise:")
    print("   • Ollama installé et en cours d'exécution")
    print("   • n8n installé et accessible (optionnel)")
    print("\n⚠️ Pour arrêter l'application: Ctrl+C")
    print("=" * 60)
    
    input("\n▶️ Appuyez sur Entrée pour démarrer l'application...")
    
    # Démarrage de l'application
    start_streamlit()
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Au revoir!")
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        sys.exit(1)
