import os

# Créer la structure de fichiers pour le projet
project_structure = {
    "dashboard_app": {
        "app.py": "",
        "pages": {
            "01_🤖_Models.py": "",
            "02_💬_Chat.py": "",
            "03_⚙️_Workflows.py": "",
            "04_📊_Monitor.py": ""
        },
        "utils": {
            "ollama_api.py": "",
            "n8n_api.py": "",
            "monitoring.py": ""
        },
        "config.py": "",
        "requirements.txt": ""
    }
}

def create_structure(structure, base_path=""):
    for name, content in structure.items():
        path = os.path.join(base_path, name)
        if isinstance(content, dict):
            os.makedirs(path, exist_ok=True)
            create_structure(content, path)
        else:
            with open(path, 'w') as f:
                f.write(content)

# Créer la structure
create_structure(project_structure)
print("Structure du projet créée avec succès!")

# Afficher la structure créée
def show_structure(path, level=0):
    for item in sorted(os.listdir(path)):
        item_path = os.path.join(path, item)
        print("  " * level + ("📁 " if os.path.isdir(item_path) else "📄 ") + item)
        if os.path.isdir(item_path):
            show_structure(item_path, level + 1)

print("\nStructure du projet:")
show_structure("dashboard_app")