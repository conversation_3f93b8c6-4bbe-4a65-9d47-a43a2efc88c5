# Créer la page de gestion des modèles Ollama
models_page_content = '''"""
Page de gestion des modèles Ollama
Interface pour télécharger, supprimer et monitorer les modèles
"""
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
import sys
import os

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from config import *
from utils.ollama_api import OllamaAPI
from utils.monitoring import monitor

st.set_page_config(
    page_title="Gestion Modèles Ollama",
    page_icon="🤖", 
    layout="wide"
)

# CSS personnalisé
st.markdown("""
<style>
    .model-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin: 1rem 0;
        border-left: 4px solid #4ECDC4;
    }
    
    .model-name {
        font-size: 1.2rem;
        font-weight: bold;
        color: #2E86AB;
        margin-bottom: 0.5rem;
    }
    
    .model-size {
        color: #666;
        font-size: 0.9rem;
    }
    
    .status-badge {
        padding: 0.2rem 0.6rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .status-running {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-stopped {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .metric-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
    }
</style>
""", unsafe_allow_html=True)

def init_session_state():
    """Initialise les variables de session"""
    if 'ollama_api' not in st.session_state:
        st.session_state.ollama_api = OllamaAPI()
    
    if 'selected_model_details' not in st.session_state:
        st.session_state.selected_model_details = None
    
    if 'download_progress' not in st.session_state:
        st.session_state.download_progress = {}

def display_model_card(model_info):
    """Affiche une carte pour un modèle"""
    model_name = model_info.get('name', 'Unknown')
    size_gb = model_info.get('size', 0) / (1024**3)
    modified_date = model_info.get('modified_at', '')
    
    if modified_date:
        try:
            # Convertir la date ISO en format lisible
            dt = datetime.fromisoformat(modified_date.replace('Z', '+00:00'))
            formatted_date = dt.strftime("%d/%m/%Y %H:%M")
        except:
            formatted_date = modified_date
    else:
        formatted_date = "Date inconnue"
    
    col1, col2, col3 = st.columns([3, 1, 1])
    
    with col1:
        st.markdown(f"""
        <div class="model-card">
            <div class="model-name">{model_name}</div>
            <div class="model-size">Taille: {size_gb:.1f} GB • Modifié: {formatted_date}</div>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        if st.button("ℹ️ Détails", key=f"details_{model_name}"):
            st.session_state.selected_model_details = model_name
    
    with col3:
        if st.button("🗑️ Supprimer", key=f"delete_{model_name}", type="secondary"):
            if st.session_state.ollama_api.delete_model(model_name):
                st.success(f"Modèle {model_name} supprimé!")
                st.rerun()
            else:
                st.error("Erreur lors de la suppression")

def display_model_details(model_name):
    """Affiche les détails d'un modèle"""
    details = st.session_state.ollama_api.get_model_info(model_name)
    
    if not details:
        st.error("Impossible de récupérer les détails du modèle")
        return
    
    st.subheader(f"📋 Détails du modèle: {model_name}")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.json({
            "Nom": details.get('model', model_name),
            "Famille": details.get('details', {}).get('family', 'N/A'),
            "Paramètres": details.get('details', {}).get('parameter_size', 'N/A'),
            "Format": details.get('details', {}).get('format', 'N/A'),
        })
    
    with col2:
        # Métriques simulées du modèle
        metrics = st.session_state.ollama_api.get_model_metrics(model_name)
        
        st.metric("Utilisation Mémoire", f"{metrics['memory_usage']} MB")
        st.metric("Tokens/sec", f"{metrics['tokens_per_second']}")
        st.metric("Temps de réponse", f"{metrics['response_time']:.1f}s")

def display_download_section():
    """Section pour télécharger de nouveaux modèles"""
    st.subheader("📥 Télécharger un Nouveau Modèle")
    
    # Modèles populaires prédéfinis
    popular_models = [
        "llama3.2:latest",
        "llama3.1:latest", 
        "mistral:latest",
        "codellama:latest",
        "phi3:latest",
        "gemma2:latest"
    ]
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        selected_model = st.selectbox(
            "Modèles populaires",
            options=popular_models,
            help="Sélectionnez un modèle prédéfini"
        )
        
        custom_model = st.text_input(
            "Ou entrez un nom de modèle personnalisé",
            placeholder="ex: llama3:8b"
        )
    
    with col2:
        st.markdown("<br>", unsafe_allow_html=True)  # Espacement
        
        model_to_download = custom_model if custom_model else selected_model
        
        if st.button("🚀 Télécharger", use_container_width=True):
            if model_to_download:
                with st.spinner(f"Téléchargement de {model_to_download}..."):
                    success = st.session_state.ollama_api.pull_model(model_to_download)
                    
                    if success:
                        st.success(f"✅ Modèle {model_to_download} téléchargé avec succès!")
                        st.rerun()
                    else:
                        st.error(f"❌ Erreur lors du téléchargement de {model_to_download}")

def display_running_models():
    """Affiche les modèles en cours d'exécution"""
    running_models = st.session_state.ollama_api.get_running_models()
    
    if running_models:
        st.subheader("🏃 Modèles en Cours d'Exécution")
        
        for model in running_models:
            col1, col2, col3 = st.columns([2, 1, 1])
            
            with col1:
                st.write(f"**{model.get('name', 'Unknown')}**")
            
            with col2:
                size_mb = model.get('size', 0) / (1024**2)
                st.write(f"{size_mb:.1f} MB")
            
            with col3:
                st.markdown('<span class="status-badge status-running">En cours</span>', 
                          unsafe_allow_html=True)
    else:
        st.info("Aucun modèle en cours d'exécution")

def display_models_analytics():
    """Affiche les analyses des modèles"""
    models = st.session_state.ollama_api.get_models()
    
    if not models:
        return
    
    st.subheader("📊 Analyse des Modèles")
    
    # Créer un DataFrame pour l'analyse
    model_data = []
    for model in models:
        size_gb = model.get('size', 0) / (1024**3)
        model_data.append({
            'Nom': model.get('name', '').split(':')[0],  # Nom sans tag
            'Taille (GB)': size_gb,
            'Modifié': model.get('modified_at', '')
        })
    
    df = pd.DataFrame(model_data)
    
    if not df.empty:
        col1, col2 = st.columns(2)
        
        with col1:
            # Graphique de distribution des tailles
            fig_size = px.bar(
                df, 
                x='Nom', 
                y='Taille (GB)',
                title="Distribution des Tailles de Modèles",
                color='Taille (GB)',
                color_continuous_scale='viridis'
            )
            fig_size.update_layout(xaxis_tickangle=-45)
            st.plotly_chart(fig_size, use_container_width=True)
        
        with col2:
            # Graphique circulaire de l'utilisation d'espace
            fig_pie = px.pie(
                df,
                values='Taille (GB)', 
                names='Nom',
                title="Répartition de l'Espace Disque"
            )
            st.plotly_chart(fig_pie, use_container_width=True)

def main():
    """Fonction principale de la page"""
    init_session_state()
    
    st.title("🤖 Gestion des Modèles Ollama")
    
    # Vérifier la connexion à Ollama
    if not st.session_state.ollama_api.is_connected():
        st.error(ERROR_MESSAGES["ollama_connection"])
        st.stop()
    
    # Métriques globales
    models = st.session_state.ollama_api.get_models()
    running_models = st.session_state.ollama_api.get_running_models()
    total_size = sum(model.get('size', 0) for model in models) / (1024**3)
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Modèles Installés", len(models))
    
    with col2:
        st.metric("Modèles Actifs", len(running_models))
    
    with col3:
        st.metric("Espace Total", f"{total_size:.1f} GB")
    
    with col4:
        # Métriques système actuelles
        metrics = monitor.get_current_metrics()
        st.metric("CPU Système", f"{metrics.cpu_percent:.1f}%")
    
    st.markdown("---")
    
    # Section de téléchargement
    display_download_section()
    
    st.markdown("---")
    
    # Modèles en cours d'exécution
    display_running_models()
    
    st.markdown("---")
    
    # Liste des modèles installés
    st.subheader("📚 Modèles Installés")
    
    if models:
        for model in models:
            display_model_card(model)
    else:
        st.info("Aucun modèle installé. Téléchargez-en un pour commencer!")
    
    # Affichage des détails si un modèle est sélectionné
    if st.session_state.selected_model_details:
        st.markdown("---")
        display_model_details(st.session_state.selected_model_details)
        
        if st.button("❌ Fermer les détails"):
            st.session_state.selected_model_details = None
            st.rerun()
    
    st.markdown("---")
    
    # Analytics des modèles
    display_models_analytics()

if __name__ == "__main__":
    main()
'''

with open("dashboard_app/pages/01_🤖_Models.py", "w", encoding='utf-8') as f:
    f.write(models_page_content)

print("✅ Page gestion des modèles créée")