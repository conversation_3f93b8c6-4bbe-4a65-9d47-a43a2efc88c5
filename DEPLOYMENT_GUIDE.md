# 🚀 Guide de Déploiement - Dashboard Ollama/n8n

## 📋 Prérequis

### Système
- **OS**: Windows, macOS, ou Linux
- **Python**: Version 3.8 ou supérieure
- **RAM**: Minimum 4GB (8GB recommandé)
- **Espace disque**: 2GB libres minimum

### Services Requis
- **Ollama**: Installé et en cours d'exécution sur le port 11434
- **n8n**: Installé et accessible sur le port 5678 (optionnel)

## 🔧 Installation

### Étape 1: Vérification de Python
```bash
python --version
# ou
python3 --version
```

### Étape 2: Installation des Dépendances
```bash
# Installation automatique
pip install -r requirements.txt

# Ou installation manuelle des packages principaux
pip install streamlit plotly pandas requests psutil ollama
```

### Étape 3: Configuration (Optionnel)
Créez un fichier `.env` pour personnaliser la configuration:
```env
OLLAMA_API_URL=http://localhost:11434
N8N_API_URL=http://localhost:5678
STREAMLIT_PORT=8501
```

## 🚀 Démarrage

### Méthode 1: Script Automatisé (Recommandé)
```bash
python start_dashboard.py
```

### Méthode 2: Démarrage Direct
```bash
streamlit run app.py
```

### Méthode 3: Démarrage avec Options
```bash
streamlit run app.py --server.port 8501 --server.address 0.0.0.0
```

## 🌐 Accès à l'Application

Une fois démarrée, l'application sera accessible à:
- **URL locale**: http://localhost:8501
- **URL réseau**: http://[votre-ip]:8501 (si configuré)

## 📱 Pages Disponibles

1. **🏠 Accueil** - Vue d'ensemble et statut des services
2. **🤖 Modèles** - Gestion des modèles Ollama
3. **💬 Chat** - Interface de conversation
4. **⚙️ Workflows** - Gestion des workflows n8n
5. **📊 Monitor** - Surveillance système

## ⚙️ Configuration Avancée

### Variables d'Environnement
```bash
# URLs des services
export OLLAMA_API_URL="http://localhost:11434"
export N8N_API_URL="http://localhost:5678"

# Configuration Streamlit
export STREAMLIT_SERVER_PORT="8501"
export STREAMLIT_SERVER_ADDRESS="0.0.0.0"

# Seuils de monitoring
export CPU_THRESHOLD="80"
export MEMORY_THRESHOLD="85"
export DISK_THRESHOLD="90"
```

### Personnalisation des Modèles
Modifiez `config.py` pour ajouter vos modèles favoris:
```python
POPULAR_MODELS = [
    "llama3.2:latest",
    "mistral:latest",
    "votre-modele:latest"
]
```

## 🔧 Dépannage

### Problème: Python non trouvé
```bash
# Windows
py --version

# macOS/Linux avec Homebrew
/usr/local/bin/python3 --version

# Utilisation d'un environnement virtuel
python -m venv dashboard_env
source dashboard_env/bin/activate  # Linux/macOS
dashboard_env\Scripts\activate     # Windows
```

### Problème: Ollama non accessible
1. Vérifiez qu'Ollama est installé: `ollama --version`
2. Démarrez Ollama: `ollama serve`
3. Testez l'API: `curl http://localhost:11434/api/tags`

### Problème: n8n non accessible
1. Vérifiez l'installation: `n8n --version`
2. Démarrez n8n: `n8n start`
3. Accédez à l'interface: http://localhost:5678

### Problème: Port déjà utilisé
```bash
# Changer le port Streamlit
streamlit run app.py --server.port 8502

# Ou modifier la configuration
export STREAMLIT_SERVER_PORT="8502"
```

## 🐳 Déploiement Docker (Optionnel)

### Dockerfile
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8501

CMD ["streamlit", "run", "app.py", "--server.address", "0.0.0.0"]
```

### Construction et Exécution
```bash
# Construction de l'image
docker build -t ollama-dashboard .

# Exécution du conteneur
docker run -p 8501:8501 ollama-dashboard
```

## 🔒 Sécurité

### Accès Réseau
- Par défaut, l'application n'est accessible que localement
- Pour un accès réseau, utilisez `--server.address 0.0.0.0`
- Considérez l'utilisation d'un reverse proxy (nginx, Apache)

### Authentification
- Streamlit ne fournit pas d'authentification native
- Pour un environnement de production, utilisez un proxy avec authentification
- Ou implémentez une authentification personnalisée

## 📊 Monitoring et Logs

### Logs Streamlit
```bash
# Logs détaillés
streamlit run app.py --logger.level debug

# Redirection des logs
streamlit run app.py > dashboard.log 2>&1
```

### Monitoring des Performances
- Utilisez la page "📊 Monitor" intégrée
- Surveillez l'utilisation CPU/RAM
- Configurez les alertes selon vos besoins

## 🔄 Mise à Jour

### Mise à Jour des Dépendances
```bash
pip install -r requirements.txt --upgrade
```

### Mise à Jour de l'Application
1. Sauvegardez votre configuration personnalisée
2. Remplacez les fichiers de l'application
3. Redémarrez le service

## 📞 Support

### Logs d'Erreur
- Consultez les logs Streamlit pour les erreurs
- Vérifiez la connectivité aux services (Ollama, n8n)
- Utilisez le script de test: `python test_app.py`

### Ressources
- Documentation Streamlit: https://docs.streamlit.io
- Documentation Ollama: https://ollama.ai/docs
- Documentation n8n: https://docs.n8n.io

## ✅ Checklist de Déploiement

- [ ] Python 3.8+ installé et accessible
- [ ] Dépendances installées (`pip install -r requirements.txt`)
- [ ] Ollama installé et en cours d'exécution
- [ ] n8n installé (optionnel)
- [ ] Ports 8501, 11434, 5678 disponibles
- [ ] Configuration personnalisée (si nécessaire)
- [ ] Test de l'application (`python test_app.py`)
- [ ] Accès à http://localhost:8501 fonctionnel

🎉 **Votre dashboard est maintenant prêt à l'emploi !**
