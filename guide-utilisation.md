# 🚀 Guide d'Utilisation du Dashboard Ollama & n8n

## 📋 Instructions de Démarrage Rapide

### 1. P<PERSON>requis
- **Ollama** installé et en fonctionnement sur le port 11434
- **n8n** installé et configuré sur le port 5678
- **Python 3.8+** avec pip

### 2. Installation et Lancement

#### Sur Linux/macOS :
```bash
cd dashboard_app
chmod +x start.sh
./start.sh
```

#### Sur Windows :
```cmd
cd dashboard_app
start.bat
```

#### Manuel :
```bash
cd dashboard_app
pip install -r requirements.txt
streamlit run app.py
```

### 3. Accès à l'Application
- Ouvrez votre navigateur
- Allez à : `http://localhost:8501`

## 🎯 Fonctionnalités Principales

### 🏠 Page d'Accueil
- **Vue d'ensemble** : Statut des services et métriques système
- **Navigation rapide** : Accès direct aux différentes sections
- **Alertes** : Notifications système en temps réel

### 🤖 Gestion des Modèles Ollama
- **Téléchargement** : Modèles populaires prédéfinis + personnalisés
- **Suppression** : Gestion de l'espace disque
- **Détails** : Informations techniques complètes
- **Métriques** : Performance et utilisation

### 💬 Interface de Chat
- **Streaming** : Réponses en temps réel
- **Historique** : Sauvegarde des conversations
- **Paramètres** : Température, tokens max, messages système
- **Presets** : Personnalités prédéfinies (assistant, expert, créatif...)
- **Export** : Téléchargement des conversations

### ⚙️ Workflows n8n
- **Visualisation** : Statut et détails des workflows
- **Déclenchement** : Exécution manuelle avec données JSON
- **Monitoring** : Suivi des exécutions en temps réel
- **Contrôles** : Activation/désactivation
- **Analytics** : Statistiques et performances

### 📊 Monitoring Système
- **Temps réel** : CPU, mémoire, disque, réseau
- **Graphiques** : Visualisations interactives
- **Alertes** : Seuils configurables
- **Processus** : Top des applications gourmandes
- **Export** : Métriques CSV et rapports

## ⚙️ Configuration Avancée

### Personnalisation des Seuils
Modifiez `config.py` :
```python
CPU_THRESHOLD = 80        # Alerte CPU à 80%
MEMORY_THRESHOLD = 85     # Alerte mémoire à 85%
```

### Ajout de Modèles
Dans `pages/01_🤖_Models.py` :
```python
popular_models = [
    "llama3.2:latest",
    "votre-modele:latest"
]
```

### Intervalles de Monitoring
```python
MONITORING_INTERVAL = 2   # Secondes entre les mesures
```

## 🔧 Résolution de Problèmes

### Ollama Non Détecté
```bash
# Vérifier le statut
ollama list

# Démarrer si nécessaire
ollama serve

# Tester l'API
curl http://localhost:11434/api/tags
```

### n8n Non Accessible
```bash
# Vérifier le port
curl http://localhost:5678/api/v1/workflows

# Redémarrer n8n si nécessaire
n8n start
```

### Erreurs de Dépendances
```bash
# Réinstaller
pip install -r requirements.txt --force-reinstall

# Ou avec environnement virtuel
python -m venv venv
source venv/bin/activate  # Linux/macOS
venv\Scripts\activate     # Windows
pip install -r requirements.txt
```

## 📊 Utilisation Optimale

### 1. Première Configuration
- Démarrez Ollama et n8n
- Téléchargez au moins un modèle
- Configurez vos seuils d'alerte
- Testez la connexion des services

### 2. Monitoring Continu
- Activez l'actualisation automatique
- Surveillez les alertes
- Exportez régulièrement les métriques
- Nettoyez les logs anciens

### 3. Gestion des Modèles
- Surveillez l'espace disque
- Supprimez les modèles inutilisés
- Testez les performances
- Documentez vos configurations

### 4. Workflows n8n
- Surveillez les exécutions
- Analysez les performances
- Gérez les erreurs rapidement
- Optimisez les workflows lents

## 🚀 Fonctionnalités Avancées

### Export de Données
- **Métriques** : Export CSV automatique
- **Rapports** : Génération de rapports système
- **Conversations** : Sauvegarde des chats
- **Logs** : Historique des workflows

### Alertes Intelligentes
- **Seuils adaptatifs** : Configuration personnalisée
- **Notifications visuelles** : Indicateurs colorés
- **Historique** : Conservation des alertes
- **Actions** : Réponses automatisées

### Interface Responsive
- **Multi-onglets** : Navigation fluide
- **Temps réel** : Mises à jour automatiques
- **Graphiques interactifs** : Zoom et exploration
- **Responsive design** : Compatible mobile/desktop

## 📈 Bonnes Pratiques

1. **Monitoring régulier** : Vérifiez quotidiennement les métriques
2. **Maintenance préventive** : Nettoyez régulièrement les données
3. **Sauvegardes** : Exportez vos configurations importantes
4. **Documentation** : Documentez vos workflows et modèles
5. **Optimisation** : Surveillez et optimisez les performances

## 🎯 Cas d'Usage

### Développement IA
- Testez différents modèles
- Comparez les performances
- Optimisez les prompts
- Surveillez l'utilisation des ressources

### Automatisation Workflows
- Surveillez les exécutions
- Déboguez les erreurs
- Optimisez les performances
- Gérez les déclenchements

### Administration Système
- Surveillez les ressources
- Gérez les alertes
- Exportez les rapports
- Maintenez la performance

---

**🎉 Profitez de votre nouveau dashboard tout-en-un !**