"""
Page de gestion des modèles Ollama
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
from datetime import datetime
import time
import json

from config import POPULAR_MODELS, DEFAULT_MESSAGES, CHART_COLORS
from ollama_api import ollama_api

# Configuration de la page
st.set_page_config(
    page_title="🤖 Gestion des Modèles",
    page_icon="🤖",
    layout="wide"
)

def format_size(size_bytes):
    """Formate la taille en bytes vers une unité lisible"""
    if size_bytes == 0:
        return "0 B"

    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} PB"

def display_model_card(model):
    """Affiche une carte pour un modèle"""
    with st.container():
        col1, col2, col3 = st.columns([3, 2, 1])

        with col1:
            st.write(f"**{model['name']}**")
            if 'modified_at' in model:
                modified = datetime.fromisoformat(model['modified_at'].replace('Z', '+00:00'))
                st.write(f"Modifié: {modified.strftime('%d/%m/%Y %H:%M')}")

        with col2:
            if 'size' in model:
                st.write(f"Taille: {format_size(model['size'])}")
            if 'digest' in model:
                st.write(f"ID: {model['digest'][:12]}...")

        with col3:
            col_info, col_delete = st.columns(2)

            with col_info:
                if st.button("ℹ️", key=f"info_{model['name']}", help="Informations détaillées"):
                    st.session_state[f"show_info_{model['name']}"] = True

            with col_delete:
                if st.button("🗑️", key=f"delete_{model['name']}", help="Supprimer le modèle"):
                    if st.session_state.get(f"confirm_delete_{model['name']}", False):
                        with st.spinner(f"Suppression de {model['name']}..."):
                            if ollama_api.delete_model(model['name']):
                                st.success(f"Modèle {model['name']} supprimé avec succès!")
                                st.rerun()
                            else:
                                st.error(f"Erreur lors de la suppression de {model['name']}")
                        st.session_state[f"confirm_delete_{model['name']}"] = False
                    else:
                        st.session_state[f"confirm_delete_{model['name']}"] = True
                        st.warning(f"Cliquez à nouveau pour confirmer la suppression de {model['name']}")

def display_model_info(model_name):
    """Affiche les informations détaillées d'un modèle"""
    if st.session_state.get(f"show_info_{model_name}", False):
        with st.expander(f"Informations détaillées - {model_name}", expanded=True):
            model_info = ollama_api.get_model_info(model_name)

            if model_info:
                col1, col2 = st.columns(2)

                with col1:
                    st.write("**Informations générales:**")
                    if 'license' in model_info:
                        st.write(f"• Licence: {model_info['license']}")
                    if 'modelfile' in model_info:
                        st.write("• Modelfile disponible")
                    if 'parameters' in model_info:
                        st.write(f"• Paramètres: {model_info['parameters']}")

                with col2:
                    st.write("**Détails techniques:**")
                    if 'details' in model_info:
                        details = model_info['details']
                        if 'format' in details:
                            st.write(f"• Format: {details['format']}")
                        if 'family' in details:
                            st.write(f"• Famille: {details['family']}")
                        if 'parameter_size' in details:
                            st.write(f"• Taille paramètres: {details['parameter_size']}")
                        if 'quantization_level' in details:
                            st.write(f"• Quantification: {details['quantization_level']}")

                # Affichage du template si disponible
                if 'template' in model_info:
                    st.write("**Template:**")
                    st.code(model_info['template'], language="text")

                # Affichage du modelfile si disponible
                if 'modelfile' in model_info:
                    st.write("**Modelfile:**")
                    st.code(model_info['modelfile'], language="dockerfile")

            if st.button("Fermer", key=f"close_info_{model_name}"):
                st.session_state[f"show_info_{model_name}"] = False
                st.rerun()

def display_download_section():
    """Affiche la section de téléchargement de modèles"""
    st.subheader("📥 Télécharger un Modèle")

    col1, col2 = st.columns([2, 1])

    with col1:
        # Sélection d'un modèle populaire ou saisie manuelle
        download_option = st.radio(
            "Choisir un modèle:",
            ["Modèle populaire", "Nom personnalisé"],
            horizontal=True
        )

        if download_option == "Modèle populaire":
            selected_model = st.selectbox(
                "Sélectionner un modèle populaire:",
                POPULAR_MODELS,
                help="Liste des modèles les plus utilisés"
            )
        else:
            selected_model = st.text_input(
                "Nom du modèle:",
                placeholder="ex: llama3.2:latest",
                help="Entrez le nom complet du modèle avec sa version"
            )

    with col2:
        st.write("**Aide:**")
        st.write("• Format: `nom:version`")
        st.write("• Exemple: `llama3.2:3b`")
        st.write("• Version par défaut: `latest`")

    if selected_model:
        if st.button(f"📥 Télécharger {selected_model}", type="primary"):
            download_model(selected_model)

def download_model(model_name):
    """Télécharge un modèle avec affichage du progrès"""
    progress_bar = st.progress(0)
    status_text = st.empty()

    try:
        for chunk in ollama_api.pull_model(model_name):
            if 'error' in chunk:
                st.error(f"Erreur: {chunk['error']}")
                break

            if 'status' in chunk:
                status_text.text(f"Statut: {chunk['status']}")

            if 'completed' in chunk and 'total' in chunk:
                progress = chunk['completed'] / chunk['total']
                progress_bar.progress(progress)

                completed_mb = chunk['completed'] / (1024 * 1024)
                total_mb = chunk['total'] / (1024 * 1024)
                status_text.text(f"Téléchargement: {completed_mb:.1f} MB / {total_mb:.1f} MB")

            if chunk.get('status') == 'success':
                progress_bar.progress(1.0)
                status_text.text("Téléchargement terminé!")
                st.success(f"Modèle {model_name} téléchargé avec succès!")
                time.sleep(2)
                st.rerun()
                break

    except Exception as e:
        st.error(f"Erreur lors du téléchargement: {e}")

def display_disk_usage_chart(models):
    """Affiche un graphique de l'utilisation de l'espace disque"""
    if not models:
        return

    st.subheader("💾 Utilisation de l'Espace Disque")

    # Préparation des données
    model_names = [model['name'] for model in models]
    model_sizes = [model.get('size', 0) for model in models]

    # Création du graphique en secteurs
    fig = go.Figure(data=[go.Pie(
        labels=model_names,
        values=model_sizes,
        hole=0.3,
        textinfo='label+percent',
        textposition='outside',
        marker=dict(colors=px.colors.qualitative.Set3)
    )])

    fig.update_layout(
        title="Répartition de l'espace disque par modèle",
        height=500,
        showlegend=True
    )

    st.plotly_chart(fig, use_container_width=True)

    # Statistiques détaillées
    col1, col2, col3 = st.columns(3)

    with col1:
        total_size = sum(model_sizes)
        st.metric("Espace total utilisé", format_size(total_size))

    with col2:
        avg_size = total_size / len(models) if models else 0
        st.metric("Taille moyenne", format_size(avg_size))

    with col3:
        largest_model = max(models, key=lambda x: x.get('size', 0)) if models else None
        if largest_model:
            st.metric("Plus gros modèle", largest_model['name'])

def display_running_models():
    """Affiche les modèles en cours d'exécution"""
    st.subheader("🏃 Modèles en Cours d'Exécution")

    running_models = ollama_api.get_running_models()

    if running_models:
        for model in running_models:
            with st.container():
                col1, col2, col3 = st.columns([3, 2, 1])

                with col1:
                    st.write(f"**{model.get('name', 'Inconnu')}**")

                with col2:
                    if 'size' in model:
                        st.write(f"Taille: {format_size(model['size'])}")

                with col3:
                    st.write("🟢 Actif")

                st.divider()
    else:
        st.info("Aucun modèle en cours d'exécution")

def main():
    """Fonction principale de la page"""

    st.title("🤖 Gestion des Modèles Ollama")

    # Vérification de la disponibilité d'Ollama
    if not ollama_api.is_available():
        st.error(DEFAULT_MESSAGES["ollama_not_available"])
        st.stop()

    # Récupération des modèles installés
    models = ollama_api.get_models()

    # Section de téléchargement
    display_download_section()

    st.divider()

    # Affichage des modèles installés
    st.subheader(f"📚 Modèles Installés ({len(models)})")

    if models:
        # Tri des modèles par nom
        models_sorted = sorted(models, key=lambda x: x['name'])

        for model in models_sorted:
            display_model_card(model)
            display_model_info(model['name'])
            st.divider()

        # Graphique d'utilisation de l'espace disque
        display_disk_usage_chart(models)

    else:
        st.info(DEFAULT_MESSAGES["no_models"])

    st.divider()

    # Modèles en cours d'exécution
    display_running_models()

if __name__ == "__main__":
    main()