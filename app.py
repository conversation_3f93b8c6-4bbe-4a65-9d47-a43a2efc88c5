"""
Dashboard Streamlit All-in-One pour Ollama et n8n
Point d'entrée principal de l'application
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime
import time

# Configuration de la page
from config import STREAMLIT_CONFIG, DEFAULT_MESSAGES, CHART_COLORS
from ollama_api import ollama_api
from n8n_api import n8n_api
from monitoring import system_monitor

# Configuration Streamlit
st.set_page_config(
    page_title=STREAMLIT_CONFIG["page_title"],
    page_icon=STREAMLIT_CONFIG["page_icon"],
    layout=STREAMLIT_CONFIG["layout"],
    initial_sidebar_state=STREAMLIT_CONFIG["initial_sidebar_state"]
)

def check_services_status():
    """Vérifie le statut des services Ollama et n8n"""
    ollama_status = ollama_api.is_available()
    n8n_status = n8n_api.is_available()

    return {
        "ollama": ollama_status,
        "n8n": n8n_status
    }

def display_service_status(services_status):
    """Affiche le statut des services"""
    col1, col2 = st.columns(2)

    with col1:
        if services_status["ollama"]:
            st.success("🟢 Ollama - Connecté")
        else:
            st.error("🔴 Ollama - Déconnecté")

    with col2:
        if services_status["n8n"]:
            st.success("🟢 n8n - Connecté")
        else:
            st.error("🔴 n8n - Déconnecté")

def display_system_overview():
    """Affiche un aperçu du système"""
    st.subheader("📊 Aperçu Système")

    # Mise à jour des métriques
    system_monitor.update_metrics()

    # Récupération des métriques actuelles
    cpu_usage = system_monitor.get_cpu_usage()
    memory_usage = system_monitor.get_memory_usage()
    disk_usage = system_monitor.get_disk_usage()
    network_usage = system_monitor.get_network_usage()

    # Affichage des métriques principales
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric(
            label="CPU",
            value=f"{cpu_usage:.1f}%",
            delta=None
        )

    with col2:
        memory_percent = memory_usage.get("percentage", 0)
        st.metric(
            label="Mémoire",
            value=f"{memory_percent:.1f}%",
            delta=None
        )

    with col3:
        avg_disk = sum(d.get("percentage", 0) for d in disk_usage) / len(disk_usage) if disk_usage else 0
        st.metric(
            label="Disque",
            value=f"{avg_disk:.1f}%",
            delta=None
        )

    with col4:
        network_total = network_usage.get("kb_sent_per_sec", 0) + network_usage.get("kb_recv_per_sec", 0)
        st.metric(
            label="Réseau",
            value=f"{network_total:.1f} KB/s",
            delta=None
        )

def display_quick_stats():
    """Affiche des statistiques rapides"""
    st.subheader("📈 Statistiques Rapides")

    col1, col2 = st.columns(2)

    with col1:
        st.write("**🤖 Modèles Ollama**")
        if ollama_api.is_available():
            models = ollama_api.get_models()
            running_models = ollama_api.get_running_models()

            st.write(f"• Modèles installés: {len(models)}")
            st.write(f"• Modèles en cours: {len(running_models)}")

            if models:
                total_size = sum(model.get("size", 0) for model in models)
                st.write(f"• Espace utilisé: {total_size / (1024**3):.1f} GB")
        else:
            st.write(DEFAULT_MESSAGES["ollama_not_available"])

    with col2:
        st.write("**⚙️ Workflows n8n**")
        if n8n_api.is_available():
            workflows = n8n_api.get_workflows()
            active_workflows = n8n_api.get_active_workflows()

            st.write(f"• Workflows totaux: {len(workflows)}")
            st.write(f"• Workflows actifs: {len(active_workflows)}")

            # Récupération des exécutions récentes
            executions = n8n_api.get_executions(limit=10)
            st.write(f"• Exécutions récentes: {len(executions)}")
        else:
            st.write(DEFAULT_MESSAGES["n8n_not_available"])

def display_recent_alerts():
    """Affiche les alertes récentes"""
    alerts = system_monitor.get_alerts()

    if alerts:
        st.subheader("🚨 Alertes Récentes")

        # Affichage des 5 dernières alertes
        for alert in alerts[-5:]:
            alert_time = alert["timestamp"].strftime("%H:%M:%S")
            st.write(f"{alert['icon']} **{alert_time}** - {alert['message']}")
    else:
        st.success("✅ Aucune alerte système")

def display_navigation_help():
    """Affiche l'aide de navigation"""
    st.subheader("🧭 Navigation")

    st.write("""
    **Pages disponibles:**

    🤖 **Modèles** - Gérez vos modèles Ollama
    - Téléchargement et suppression de modèles
    - Monitoring des performances
    - Informations détaillées

    💬 **Chat** - Interface de conversation
    - Chat en temps réel avec streaming
    - Support de multiples modèles
    - Historique et export des conversations

    ⚙️ **Workflows** - Gestion n8n
    - Liste et statut des workflows
    - Déclenchement manuel
    - Monitoring des exécutions

    📊 **Monitor** - Surveillance système
    - Métriques en temps réel
    - Graphiques interactifs
    - Alertes configurables
    """)

def main():
    """Fonction principale de l'application"""

    # Titre principal
    st.title("🚀 Dashboard All-in-One")
    st.markdown("*Gestion complète d'Ollama et n8n*")

    # Message de bienvenue
    st.info(DEFAULT_MESSAGES["welcome"])

    # Vérification du statut des services
    services_status = check_services_status()

    # Affichage du statut des services
    st.subheader("🔌 Statut des Services")
    display_service_status(services_status)

    # Séparateur
    st.divider()

    # Aperçu système
    display_system_overview()

    # Séparateur
    st.divider()

    # Statistiques rapides
    display_quick_stats()

    # Séparateur
    st.divider()

    # Alertes récentes
    display_recent_alerts()

    # Séparateur
    st.divider()

    # Aide de navigation
    display_navigation_help()

    # Informations de bas de page
    st.markdown("---")
    st.markdown(
        """
        <div style='text-align: center; color: #666;'>
            <p>Dashboard développé avec ❤️ pour la communauté open source</p>
            <p>Streamlit • Ollama • n8n</p>
        </div>
        """,
        unsafe_allow_html=True
    )

if __name__ == "__main__":
    main()