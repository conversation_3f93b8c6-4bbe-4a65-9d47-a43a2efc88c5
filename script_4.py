# Créer le module de monitoring
monitoring_content = '''"""
Module de monitoring système et surveillance des erreurs
Utilise psutil pour les métriques système et gère les alertes
"""
import psutil
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import streamlit as st
from dataclasses import dataclass
from config import *

@dataclass
class SystemMetrics:
    """Structure pour les métriques système"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    disk_usage: float
    network_sent: int
    network_recv: int
    process_count: int
    ollama_running: bool
    n8n_running: bool

class SystemMonitor:
    def __init__(self):
        self.metrics_history: List[SystemMetrics] = []
        self.alerts: List[Dict[str, Any]] = []
        self.last_network_stats = psutil.net_io_counters()
        self.last_check_time = time.time()
    
    def get_current_metrics(self) -> SystemMetrics:
        """Récupère les métriques système actuelles"""
        # CPU
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Mémoire
        memory = psutil.virtual_memory()
        
        # Disque
        disk = psutil.disk_usage('/')
        
        # Réseau
        current_network = psutil.net_io_counters()
        current_time = time.time()
        time_delta = current_time - self.last_check_time
        
        network_sent = (current_network.bytes_sent - self.last_network_stats.bytes_sent) / time_delta if time_delta > 0 else 0
        network_recv = (current_network.bytes_recv - self.last_network_stats.bytes_recv) / time_delta if time_delta > 0 else 0
        
        self.last_network_stats = current_network
        self.last_check_time = current_time
        
        # Processus
        process_count = len(psutil.pids())
        
        # Vérification des services
        ollama_running = self._is_process_running("ollama")
        n8n_running = self._is_process_running("n8n")
        
        metrics = SystemMetrics(
            timestamp=datetime.now(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            disk_usage=(disk.used / disk.total) * 100,
            network_sent=int(network_sent),
            network_recv=int(network_recv),
            process_count=process_count,
            ollama_running=ollama_running,
            n8n_running=n8n_running
        )
        
        # Ajouter à l'historique (garder seulement les 100 dernières)
        self.metrics_history.append(metrics)
        if len(self.metrics_history) > 100:
            self.metrics_history.pop(0)
        
        # Vérifier les seuils et créer des alertes
        self._check_thresholds(metrics)
        
        return metrics
    
    def _is_process_running(self, process_name: str) -> bool:
        """Vérifie si un processus est en cours d'exécution"""
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                if process_name.lower() in proc.info['name'].lower():
                    return True
        except:
            pass
        return False
    
    def _check_thresholds(self, metrics: SystemMetrics):
        """Vérifie les seuils et crée des alertes"""
        alerts = []
        
        # Alerte CPU
        if metrics.cpu_percent > CPU_THRESHOLD:
            alerts.append({
                "type": "warning",
                "message": f"CPU usage élevé: {metrics.cpu_percent:.1f}%",
                "timestamp": metrics.timestamp,
                "severity": "high" if metrics.cpu_percent > 90 else "medium"
            })
        
        # Alerte mémoire
        if metrics.memory_percent > MEMORY_THRESHOLD:
            alerts.append({
                "type": "warning", 
                "message": f"Utilisation mémoire élevée: {metrics.memory_percent:.1f}%",
                "timestamp": metrics.timestamp,
                "severity": "high" if metrics.memory_percent > 95 else "medium"
            })
        
        # Alerte services
        if not metrics.ollama_running:
            alerts.append({
                "type": "error",
                "message": "Service Ollama non détecté",
                "timestamp": metrics.timestamp,
                "severity": "high"
            })
        
        if not metrics.n8n_running:
            alerts.append({
                "type": "error",
                "message": "Service n8n non détecté", 
                "timestamp": metrics.timestamp,
                "severity": "high"
            })
        
        # Ajouter les nouvelles alertes
        self.alerts.extend(alerts)
        
        # Garder seulement les alertes des dernières 24h
        cutoff_time = datetime.now() - timedelta(hours=24)
        self.alerts = [a for a in self.alerts if a["timestamp"] > cutoff_time]
    
    def get_system_info(self) -> Dict[str, Any]:
        """Récupère les informations système"""
        try:
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            boot_time = datetime.fromtimestamp(psutil.boot_time())
            
            return {
                "cpu_cores": cpu_count,
                "cpu_frequency": f"{cpu_freq.current:.0f} MHz" if cpu_freq else "N/A",
                "total_memory": f"{memory.total / (1024**3):.1f} GB",
                "total_disk": f"{disk.total / (1024**3):.1f} GB",
                "boot_time": boot_time.strftime("%Y-%m-%d %H:%M:%S"),
                "uptime": str(datetime.now() - boot_time).split('.')[0],
                "platform": psutil.os.name
            }
        except Exception as e:
            st.error(f"Erreur lors de la récupération des infos système: {e}")
            return {}
    
    def get_process_info(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Récupère les informations des processus les plus gourmands"""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    pinfo = proc.info
                    if pinfo['cpu_percent'] is not None and pinfo['cpu_percent'] > 0:
                        processes.append(pinfo)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            # Trier par utilisation CPU
            processes.sort(key=lambda x: x['cpu_percent'], reverse=True)
            return processes[:limit]
        except Exception as e:
            st.error(f"Erreur lors de la récupération des processus: {e}")
            return []
    
    def get_recent_alerts(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Récupère les alertes récentes"""
        sorted_alerts = sorted(self.alerts, key=lambda x: x["timestamp"], reverse=True)
        return sorted_alerts[:limit]
    
    def clear_alerts(self):
        """Efface toutes les alertes"""
        self.alerts.clear()
    
    def export_metrics_to_csv(self) -> str:
        """Exporte les métriques vers un fichier CSV"""
        if not self.metrics_history:
            return ""
        
        import pandas as pd
        
        data = []
        for metric in self.metrics_history:
            data.append({
                "timestamp": metric.timestamp.isoformat(),
                "cpu_percent": metric.cpu_percent,
                "memory_percent": metric.memory_percent,
                "disk_usage": metric.disk_usage,
                "network_sent": metric.network_sent,
                "network_recv": metric.network_recv,
                "process_count": metric.process_count,
                "ollama_running": metric.ollama_running,
                "n8n_running": metric.n8n_running
            })
        
        df = pd.DataFrame(data)
        csv_content = df.to_csv(index=False)
        return csv_content
    
    def get_metrics_for_chart(self, metric_type: str, last_n: int = 50) -> List[Dict[str, Any]]:
        """Récupère les métriques formatées pour les graphiques"""
        if not self.metrics_history:
            return []
        
        recent_metrics = self.metrics_history[-last_n:]
        chart_data = []
        
        for metric in recent_metrics:
            data_point = {
                "time": metric.timestamp.strftime("%H:%M:%S"),
                "timestamp": metric.timestamp
            }
            
            if metric_type == "cpu":
                data_point["value"] = metric.cpu_percent
                data_point["label"] = "CPU %"
            elif metric_type == "memory":
                data_point["value"] = metric.memory_percent  
                data_point["label"] = "Memory %"
            elif metric_type == "disk":
                data_point["value"] = metric.disk_usage
                data_point["label"] = "Disk %"
            elif metric_type == "network":
                data_point["sent"] = metric.network_sent / 1024  # KB/s
                data_point["recv"] = metric.network_recv / 1024  # KB/s
            
            chart_data.append(data_point)
        
        return chart_data

# Instance globale du moniteur
monitor = SystemMonitor()
'''

with open("dashboard_app/utils/monitoring.py", "w", encoding='utf-8') as f:
    f.write(monitoring_content)

print("✅ Module de monitoring créé")