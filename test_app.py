"""
Script de test pour valider l'application Dashboard
"""

import sys
import importlib.util
import traceback

def test_import(module_name, file_path):
    """Teste l'importation d'un module"""
    try:
        spec = importlib.util.spec_from_file_location(module_name, file_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        print(f"✅ {module_name}: Import réussi")
        return True
    except Exception as e:
        print(f"❌ {module_name}: Erreur d'import - {e}")
        return False

def test_config():
    """Teste le module de configuration"""
    try:
        import config
        
        # Vérification des variables principales
        required_vars = [
            'OLLAMA_API_URL', 'N8N_API_URL', 'STREAMLIT_CONFIG',
            'POPULAR_MODELS', 'CHAT_CONFIG', 'MONITORING_CONFIG'
        ]
        
        for var in required_vars:
            if hasattr(config, var):
                print(f"✅ Config: {var} défini")
            else:
                print(f"❌ Config: {var} manquant")
                return False
        
        return True
    except Exception as e:
        print(f"❌ Config: Erreur - {e}")
        return False

def test_api_modules():
    """Teste les modules API"""
    try:
        from ollama_api import ollama_api
        from n8n_api import n8n_api
        from monitoring import system_monitor
        
        print("✅ Modules API: Imports réussis")
        
        # Test des méthodes principales
        print(f"✅ Ollama API: Méthodes disponibles - {len(dir(ollama_api))}")
        print(f"✅ n8n API: Méthodes disponibles - {len(dir(n8n_api))}")
        print(f"✅ System Monitor: Méthodes disponibles - {len(dir(system_monitor))}")
        
        return True
    except Exception as e:
        print(f"❌ Modules API: Erreur - {e}")
        traceback.print_exc()
        return False

def test_pages():
    """Teste les pages Streamlit"""
    pages = [
        ("app.py", "Application principale"),
        ("01_🤖_Models.py", "Page Modèles"),
        ("02_💬_Chat.py", "Page Chat"),
        ("03_⚙️_Workflows.py", "Page Workflows"),
        ("04_📊_Monitor.py", "Page Monitoring")
    ]
    
    results = []
    for file_path, description in pages:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Vérifications basiques
            if 'import streamlit as st' in content:
                print(f"✅ {description}: Import Streamlit trouvé")
            else:
                print(f"❌ {description}: Import Streamlit manquant")
                results.append(False)
                continue
            
            if 'def main():' in content:
                print(f"✅ {description}: Fonction main() trouvée")
            else:
                print(f"❌ {description}: Fonction main() manquante")
                results.append(False)
                continue
            
            if '__name__ == "__main__"' in content:
                print(f"✅ {description}: Point d'entrée trouvé")
            else:
                print(f"❌ {description}: Point d'entrée manquant")
                results.append(False)
                continue
            
            results.append(True)
            print(f"✅ {description}: Structure valide")
            
        except Exception as e:
            print(f"❌ {description}: Erreur - {e}")
            results.append(False)
    
    return all(results)

def test_requirements():
    """Teste le fichier requirements.txt"""
    try:
        with open('requirements.txt', 'r', encoding='utf-8') as f:
            requirements = f.read().strip().split('\n')
        
        required_packages = [
            'streamlit', 'plotly', 'pandas', 'requests', 
            'psutil', 'ollama'
        ]
        
        found_packages = []
        for req in requirements:
            if req.strip() and not req.startswith('#'):
                package_name = req.split('==')[0].split('>=')[0].split('<=')[0]
                found_packages.append(package_name.lower())
        
        for package in required_packages:
            if package in found_packages:
                print(f"✅ Requirements: {package} trouvé")
            else:
                print(f"❌ Requirements: {package} manquant")
        
        print(f"✅ Requirements: {len(found_packages)} packages définis")
        return True
        
    except Exception as e:
        print(f"❌ Requirements: Erreur - {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🚀 Test de validation de l'application Dashboard")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_config),
        ("Modules API", test_api_modules),
        ("Pages Streamlit", test_pages),
        ("Requirements", test_requirements)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Test: {test_name}")
        print("-" * 30)
        result = test_func()
        results.append(result)
        print(f"Résultat: {'✅ RÉUSSI' if result else '❌ ÉCHEC'}")
    
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 50)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ RÉUSSI" if results[i] else "❌ ÉCHEC"
        print(f"{test_name}: {status}")
    
    success_rate = sum(results) / len(results) * 100
    print(f"\nTaux de réussite: {success_rate:.1f}%")
    
    if all(results):
        print("\n🎉 Tous les tests sont réussis! L'application est prête.")
        return True
    else:
        print("\n⚠️ Certains tests ont échoué. Vérifiez les erreurs ci-dessus.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
