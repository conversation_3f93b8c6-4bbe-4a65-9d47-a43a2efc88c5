# Créer la page de gestion des workflows n8n
workflows_page_content = '''"""
Page de gestion et monitoring des workflows n8n
Interface pour déclencher, surveiller et analyser les workflows
"""
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import json
import sys
import os

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from config import *
from utils.n8n_api import N8nAPI

st.set_page_config(
    page_title="Workflows n8n", 
    page_icon="⚙️",
    layout="wide"
)

# CSS personnalisé
st.markdown("""
<style>
    .workflow-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin: 1rem 0;
        border-left: 4px solid #4ECDC4;
    }
    
    .workflow-active {
        border-left-color: #4CAF50;
        background: linear-gradient(135deg, #e8f5e9 0%, #f1f8e9 100%);
    }
    
    .workflow-inactive {
        border-left-color: #f44336;
        background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
    }
    
    .status-badge {
        padding: 0.3rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
        text-transform: uppercase;
    }
    
    .status-active {
        background-color: #4CAF50;
        color: white;
    }
    
    .status-inactive {
        background-color: #f44336;
        color: white;
    }
    
    .status-running {
        background-color: #2196F3;
        color: white;
    }
    
    .execution-success {
        background-color: #d4edda;
        border-left: 4px solid #28a745;
        padding: 0.5rem;
        margin: 0.2rem 0;
        border-radius: 4px;
    }
    
    .execution-error {
        background-color: #f8d7da;
        border-left: 4px solid #dc3545;
        padding: 0.5rem;
        margin: 0.2rem 0;
        border-radius: 4px;
    }
    
    .execution-running {
        background-color: #cce5ff;
        border-left: 4px solid #007bff;
        padding: 0.5rem;
        margin: 0.2rem 0;
        border-radius: 4px;
    }
    
    .metric-dashboard {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 12px;
        text-align: center;
        margin: 0.5rem 0;
    }
    
    .log-entry {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        margin: 0.5rem 0;
        font-family: monospace;
        font-size: 0.9rem;
    }
</style>
""", unsafe_allow_html=True)

def init_session_state():
    """Initialise les variables de session"""
    if 'n8n_api' not in st.session_state:
        st.session_state.n8n_api = N8nAPI()
    
    if 'selected_workflow' not in st.session_state:
        st.session_state.selected_workflow = None
    
    if 'trigger_data' not in st.session_state:
        st.session_state.trigger_data = "{}"

def display_workflow_card(workflow):
    """Affiche une carte pour un workflow"""
    workflow_id = workflow.get('id', '')
    workflow_name = workflow.get('name', 'Sans nom')
    is_active = workflow.get('active', False)
    created_at = workflow.get('createdAt', '')
    updated_at = workflow.get('updatedAt', '')
    
    # Formatage des dates
    try:
        if created_at:
            created_dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
            created_formatted = created_dt.strftime("%d/%m/%Y %H:%M")
        else:
            created_formatted = "Date inconnue"
    except:
        created_formatted = created_at
    
    status_class = "workflow-active" if is_active else "workflow-inactive"
    badge_class = "status-active" if is_active else "status-inactive"
    status_text = "ACTIF" if is_active else "INACTIF"
    
    col1, col2, col3, col4 = st.columns([3, 1, 1, 1])
    
    with col1:
        st.markdown(f"""
        <div class="workflow-card {status_class}">
            <h4 style="margin: 0 0 0.5rem 0;">{workflow_name}</h4>
            <p style="margin: 0; color: #666; font-size: 0.9rem;">
                ID: {workflow_id}<br>
                Créé: {created_formatted}
            </p>
            <span class="status-badge {badge_class}">{status_text}</span>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        if st.button("📊 Stats", key=f"stats_{workflow_id}"):
            st.session_state.selected_workflow = workflow_id
    
    with col3:
        if st.button("▶️ Lancer", key=f"trigger_{workflow_id}"):
            trigger_workflow(workflow_id, workflow_name)
    
    with col4:
        # Bouton toggle actif/inactif
        if is_active:
            if st.button("⏸️ Pause", key=f"pause_{workflow_id}"):
                if st.session_state.n8n_api.deactivate_workflow(workflow_id):
                    st.success(f"Workflow {workflow_name} désactivé")
                    st.rerun()
        else:
            if st.button("▶️ Activer", key=f"activate_{workflow_id}"):
                if st.session_state.n8n_api.activate_workflow(workflow_id):
                    st.success(f"Workflow {workflow_name} activé")
                    st.rerun()

def trigger_workflow(workflow_id: str, workflow_name: str):
    """Déclenche l'exécution d'un workflow"""
    try:
        # Essayer de parser les données JSON
        trigger_data = json.loads(st.session_state.trigger_data) if st.session_state.trigger_data else {}
        
        result = st.session_state.n8n_api.trigger_workflow(workflow_id, trigger_data)
        
        if 'error' not in result:
            st.success(f"✅ Workflow '{workflow_name}' déclenché avec succès!")
            st.json(result)
        else:
            st.error(f"❌ Erreur: {result['error']}")
    
    except json.JSONDecodeError:
        st.error("❌ Format JSON invalide dans les données de déclenchement")
    except Exception as e:
        st.error(f"❌ Erreur lors du déclenchement: {e}")

def display_workflow_statistics():
    """Affiche les statistiques globales des workflows"""
    stats = st.session_state.n8n_api.get_workflow_statistics()
    
    if not stats:
        st.warning("Impossible de récupérer les statistiques")
        return
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Workflows", stats.get('total_workflows', 0))
    
    with col2:
        st.metric("Workflows Actifs", stats.get('active_workflows', 0))
    
    with col3:
        st.metric("Exécutions Réussies", stats.get('successful_executions', 0))
    
    with col4:
        success_rate = stats.get('success_rate', 0)
        st.metric("Taux de Réussite", f"{success_rate:.1f}%")

def display_recent_executions():
    """Affiche les exécutions récentes"""
    st.subheader("🔄 Exécutions Récentes")
    
    executions = st.session_state.n8n_api.get_executions(limit=20)
    
    if not executions:
        st.info("Aucune exécution récente trouvée")
        return
    
    for execution in executions:
        execution_id = execution.get('id', 'N/A')
        workflow_id = execution.get('workflowId', 'N/A')
        started_at = execution.get('startedAt', '')
        finished = execution.get('finished', False)
        error = execution.get('error')
        
        # Déterminer le statut
        if error:
            status = "error"
            status_text = "❌ ÉCHEC"
            css_class = "execution-error"
        elif finished:
            status = "success"
            status_text = "✅ RÉUSSI"
            css_class = "execution-success"
        else:
            status = "running"
            status_text = "🔄 EN COURS"
            css_class = "execution-running"
        
        # Formatage de la date
        try:
            if started_at:
                started_dt = datetime.fromisoformat(started_at.replace('Z', '+00:00'))
                time_str = started_dt.strftime("%d/%m %H:%M:%S")
            else:
                time_str = "Date inconnue"
        except:
            time_str = started_at
        
        st.markdown(f"""
        <div class="{css_class}">
            <strong>{status_text}</strong> - Workflow ID: {workflow_id}<br>
            Exécution ID: {execution_id}<br>
            Démarré: {time_str}
            {f'<br><strong>Erreur:</strong> {error.get("message", "Erreur inconnue")}' if error else ''}
        </div>
        """, unsafe_allow_html=True)

def display_active_executions():
    """Affiche les exécutions en cours"""
    st.subheader("⚡ Exécutions en Cours")
    
    active_executions = st.session_state.n8n_api.get_active_executions()
    
    if not active_executions:
        st.success("✅ Aucune exécution en cours")
        return
    
    for execution in active_executions:
        execution_id = execution.get('id', 'N/A')
        workflow_id = execution.get('workflowId', 'N/A')
        started_at = execution.get('startedAt', '')
        
        try:
            if started_at:
                started_dt = datetime.fromisoformat(started_at.replace('Z', '+00:00'))
                duration = datetime.now() - started_dt.replace(tzinfo=None)
                duration_str = str(duration).split('.')[0]
            else:
                duration_str = "Durée inconnue"
        except:
            duration_str = "Erreur de calcul"
        
        st.markdown(f"""
        <div class="execution-running">
            <strong>🔄 EN COURS</strong><br>
            Workflow ID: {workflow_id}<br>
            Exécution ID: {execution_id}<br>
            Durée: {duration_str}
        </div>
        """, unsafe_allow_html=True)

def display_workflow_details(workflow_id: str):
    """Affiche les détails d'un workflow sélectionné"""
    workflow_details = st.session_state.n8n_api.get_workflow_details(workflow_id)
    
    if not workflow_details:
        st.error("Impossible de récupérer les détails du workflow")
        return
    
    st.subheader(f"📋 Détails du Workflow: {workflow_details.get('name', 'Sans nom')}")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.json({
            "ID": workflow_details.get('id'),
            "Nom": workflow_details.get('name'),
            "Actif": workflow_details.get('active'),
            "Créé": workflow_details.get('createdAt'),
            "Modifié": workflow_details.get('updatedAt')
        })
    
    with col2:
        # Logs du workflow
        st.subheader("📜 Logs Récents")
        logs = st.session_state.n8n_api.get_workflow_logs(workflow_id, limit=10)
        
        for log in logs:
            status_icon = "✅" if log['status'] == 'success' else "❌"
            duration = f"{log['duration']:.1f}s" if log['duration'] else "N/A"
            
            st.markdown(f"""
            <div class="log-entry">
                {status_icon} {log['start_time']} - Durée: {duration}
                {f'<br>❌ Erreur: {log["error"]}' if log['error'] else ''}
            </div>
            """, unsafe_allow_html=True)

def display_workflow_analytics():
    """Affiche les analyses des workflows"""
    st.subheader("📊 Analyses des Workflows")
    
    # Récupérer les données
    workflows = st.session_state.n8n_api.get_workflows()
    executions = st.session_state.n8n_api.get_executions(limit=100)
    
    if not workflows or not executions:
        st.info("Données insuffisantes pour l'analyse")
        return
    
    col1, col2 = st.columns(2)
    
    with col1:
        # Graphique de répartition actif/inactif
        active_count = len([w for w in workflows if w.get('active', False)])
        inactive_count = len(workflows) - active_count
        
        fig_status = px.pie(
            values=[active_count, inactive_count],
            names=['Actifs', 'Inactifs'],
            title="Répartition des Workflows",
            color_discrete_map={
                'Actifs': '#4CAF50',
                'Inactifs': '#f44336'
            }
        )
        st.plotly_chart(fig_status, use_container_width=True)
    
    with col2:
        # Graphique des exécutions par statut
        success_count = len([e for e in executions if e.get('finished') and not e.get('error')])
        error_count = len([e for e in executions if e.get('error')])
        running_count = len([e for e in executions if not e.get('finished')])
        
        fig_exec = px.bar(
            x=['Réussies', 'Échecs', 'En cours'],
            y=[success_count, error_count, running_count],
            title="Statut des Exécutions",
            color=['Réussies', 'Échecs', 'En cours'],
            color_discrete_map={
                'Réussies': '#4CAF50',
                'Échecs': '#f44336',
                'En cours': '#2196F3'
            }
        )
        st.plotly_chart(fig_exec, use_container_width=True)

def display_trigger_section():
    """Affiche la section de déclenchement manuel"""
    st.subheader("🚀 Déclenchement Manuel")
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        trigger_data = st.text_area(
            "Données JSON à envoyer (optionnel)",
            value=st.session_state.trigger_data,
            height=100,
            help="Format JSON pour les données à passer au workflow"
        )
        
        st.session_state.trigger_data = trigger_data
    
    with col2:
        st.markdown("**Exemples de données:**")
        
        examples = {
            "Simple": '{"message": "Hello World"}',
            "Utilisateur": '{"user": {"name": "John", "email": "<EMAIL>"}}',
            "Données": '{"data": [1, 2, 3], "action": "process"}'
        }
        
        for name, example in examples.items():
            if st.button(f"📝 {name}", key=f"example_{name}"):
                st.session_state.trigger_data = example
                st.rerun()

def main():
    """Fonction principale de la page workflows"""
    init_session_state()
    
    st.title("⚙️ Gestion des Workflows n8n")
    
    # Vérifier la connexion à n8n
    if not st.session_state.n8n_api.is_connected():
        st.error(ERROR_MESSAGES["n8n_connection"])
        st.stop()
    
    # Statistiques globales
    display_workflow_statistics()
    
    st.markdown("---")
    
    # Section de déclenchement manuel
    display_trigger_section()
    
    st.markdown("---")
    
    # Workflows
    st.subheader("📋 Liste des Workflows")
    
    workflows = st.session_state.n8n_api.get_workflows()
    
    if workflows:
        for workflow in workflows:
            display_workflow_card(workflow)
    else:
        st.info("Aucun workflow trouvé")
    
    # Détails du workflow sélectionné
    if st.session_state.selected_workflow:
        st.markdown("---")
        display_workflow_details(st.session_state.selected_workflow)
        
        if st.button("❌ Fermer les détails"):
            st.session_state.selected_workflow = None
            st.rerun()
    
    st.markdown("---")
    
    # Exécutions
    col1, col2 = st.columns(2)
    
    with col1:
        display_active_executions()
    
    with col2:
        display_recent_executions()
    
    st.markdown("---")
    
    # Analytics
    display_workflow_analytics()

if __name__ == "__main__":
    main()
'''

with open("dashboard_app/pages/03_⚙️_Workflows.py", "w", encoding='utf-8') as f:
    f.write(workflows_page_content)

print("✅ Page gestion des workflows n8n créée")