# <PERSON><PERSON><PERSON> le fichier app.py principal
app_content = '''"""
Dashboard Streamlit All-in-One pour Ollama et n8n
Point d'entrée principal de l'application
"""
import streamlit as st
import sys
import os

# Ajouter le répertoire utils au path
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

from config import *
from utils.ollama_api import OllamaAPI
from utils.n8n_api import N8nAPI
from utils.monitoring import monitor

# Configuration de la page
st.set_page_config(
    page_title=APP_TITLE,
    page_icon=APP_ICON,
    layout=APP_LAYOUT,
    initial_sidebar_state="expanded"
)

# CSS personnalisé
st.markdown("""
<style>
    .main-header {
        text-align: center;
        padding: 1rem 0;
        background: linear-gradient(90deg, #FF6B6B, #4ECDC4);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 2rem;
    }
    
    .metric-card {
        background: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #4ECDC4;
    }
    
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .status-online { background-color: #4CAF50; }
    .status-offline { background-color: #f44336; }
    
    .alert-high { background-color: #ffebee; border-left: 4px solid #f44336; }
    .alert-medium { background-color: #fff3e0; border-left: 4px solid #ff9800; }
    .alert-low { background-color: #e8f5e8; border-left: 4px solid #4caf50; }
</style>
""", unsafe_allow_html=True)

def init_session_state():
    """Initialise les variables de session"""
    if 'ollama_api' not in st.session_state:
        st.session_state.ollama_api = OllamaAPI()
    
    if 'n8n_api' not in st.session_state:
        st.session_state.n8n_api = N8nAPI()
    
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []
    
    if 'selected_model' not in st.session_state:
        st.session_state.selected_model = None
    
    if 'system_metrics' not in st.session_state:
        st.session_state.system_metrics = None

def check_services_status():
    """Vérifie le statut des services"""
    ollama_status = st.session_state.ollama_api.is_connected()
    n8n_status = st.session_state.n8n_api.is_connected()
    
    return ollama_status, n8n_status

def display_service_status(ollama_status: bool, n8n_status: bool):
    """Affiche le statut des services"""
    col1, col2 = st.columns(2)
    
    with col1:
        status_class = "status-online" if ollama_status else "status-offline"
        status_text = "En ligne" if ollama_status else "Hors ligne"
        st.markdown(f"""
        <div class="metric-card">
            <span class="status-indicator {status_class}"></span>
            <strong>Ollama:</strong> {status_text}
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        status_class = "status-online" if n8n_status else "status-offline"
        status_text = "En ligne" if n8n_status else "Hors ligne"
        st.markdown(f"""
        <div class="metric-card">
            <span class="status-indicator {status_class}"></span>
            <strong>n8n:</strong> {status_text}
        </div>
        """, unsafe_allow_html=True)

def display_quick_metrics():
    """Affiche les métriques rapides du système"""
    metrics = monitor.get_current_metrics()
    st.session_state.system_metrics = metrics
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("CPU", f"{metrics.cpu_percent:.1f}%", 
                 delta=None, delta_color="inverse")
    
    with col2:
        st.metric("Mémoire", f"{metrics.memory_percent:.1f}%", 
                 delta=None, delta_color="inverse")
    
    with col3:
        models = st.session_state.ollama_api.get_models()
        st.metric("Modèles Ollama", len(models))
    
    with col4:
        workflows = st.session_state.n8n_api.get_workflows()
        st.metric("Workflows n8n", len(workflows))

def display_recent_alerts():
    """Affiche les alertes récentes"""
    alerts = monitor.get_recent_alerts(limit=5)
    
    if alerts:
        st.subheader("🚨 Alertes Récentes")
        for alert in alerts:
            severity_class = f"alert-{alert.get('severity', 'low')}"
            timestamp = alert['timestamp'].strftime("%H:%M:%S")
            
            st.markdown(f"""
            <div class="metric-card {severity_class}">
                <strong>{timestamp}</strong> - {alert['message']}
            </div>
            """, unsafe_allow_html=True)
    else:
        st.success("✅ Aucune alerte système")

def main():
    """Fonction principale"""
    # Initialisation
    init_session_state()
    
    # En-tête principal
    st.markdown(f'<h1 class="main-header">{APP_TITLE}</h1>', unsafe_allow_html=True)
    
    # Vérification des services
    ollama_status, n8n_status = check_services_status()
    
    # Affichage du statut des services
    st.subheader("🔧 Statut des Services")
    display_service_status(ollama_status, n8n_status)
    
    st.markdown("---")
    
    # Métriques système rapides
    st.subheader("📊 Métriques Système")
    display_quick_metrics()
    
    st.markdown("---")
    
    # Alertes récentes
    display_recent_alerts()
    
    st.markdown("---")
    
    # Navigation vers les pages
    st.subheader("🚀 Navigation Rapide")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🤖 Gestion Modèles", use_container_width=True):
            st.switch_page("pages/01_🤖_Models.py")
    
    with col2:
        if st.button("💬 Interface Chat", use_container_width=True):
            st.switch_page("pages/02_💬_Chat.py")
    
    with col3:
        if st.button("⚙️ Workflows n8n", use_container_width=True):
            st.switch_page("pages/03_⚙️_Workflows.py")
    
    with col4:
        if st.button("📊 Monitoring", use_container_width=True):
            st.switch_page("pages/04_📊_Monitor.py")
    
    # Informations supplémentaires dans la sidebar
    with st.sidebar:
        st.header("ℹ️ Informations")
        
        system_info = monitor.get_system_info()
        for key, value in system_info.items():
            st.info(f"**{key.replace('_', ' ').title()}:** {value}")
        
        st.markdown("---")
        
        # Bouton de rafraîchissement
        if st.button("🔄 Actualiser", use_container_width=True):
            st.rerun()
        
        # Bouton pour effacer les alertes
        if st.button("🗑️ Effacer Alertes", use_container_width=True):
            monitor.clear_alerts()
            st.success("Alertes effacées!")
            st.rerun()

if __name__ == "__main__":
    main()
'''

with open("dashboard_app/app.py", "w", encoding='utf-8') as f:
    f.write(app_content)

print("✅ Fichier app.py principal créé")