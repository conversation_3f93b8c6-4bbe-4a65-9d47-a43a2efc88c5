"""
Module de monitoring système
"""

import psutil
import time
import platform
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import streamlit as st
from config import (
    CPU_THRESHOLD, MEMORY_THRESHOLD, DISK_THRESHOLD, NETWORK_THRESHOLD,
    MONITORING_CONFIG, ALERT_CONFIG
)


class SystemMonitor:
    """Classe pour le monitoring système"""

    def __init__(self):
        self.history = {
            "cpu": [],
            "memory": [],
            "disk": [],
            "network": [],
            "timestamps": []
        }
        self.alerts = []
        self.last_network_stats = None
        self.max_history = MONITORING_CONFIG["history_length"]

    def get_system_info(self) -> Dict:
        """Récupère les informations système de base"""
        try:
            return {
                "platform": platform.system(),
                "platform_release": platform.release(),
                "platform_version": platform.version(),
                "architecture": platform.machine(),
                "hostname": platform.node(),
                "processor": platform.processor(),
                "cpu_count": psutil.cpu_count(),
                "cpu_count_logical": psutil.cpu_count(logical=True),
                "boot_time": datetime.fromtimestamp(psutil.boot_time()),
                "uptime": datetime.now() - datetime.fromtimestamp(psutil.boot_time())
            }
        except Exception as e:
            st.error(f"Erreur lors de la récupération des infos système: {e}")
            return {}

    def get_cpu_usage(self) -> float:
        """Récupère l'utilisation CPU"""
        try:
            return psutil.cpu_percent(interval=1)
        except Exception as e:
            st.error(f"Erreur lors de la récupération du CPU: {e}")
            return 0.0

    def get_memory_usage(self) -> Dict:
        """Récupère l'utilisation mémoire"""
        try:
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()

            return {
                "total": memory.total,
                "available": memory.available,
                "used": memory.used,
                "percentage": memory.percent,
                "swap_total": swap.total,
                "swap_used": swap.used,
                "swap_percentage": swap.percent
            }
        except Exception as e:
            st.error(f"Erreur lors de la récupération de la mémoire: {e}")
            return {}

    def get_disk_usage(self) -> List[Dict]:
        """Récupère l'utilisation disque pour toutes les partitions"""
        try:
            disks = []
            partitions = psutil.disk_partitions()

            for partition in partitions:
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disks.append({
                        "device": partition.device,
                        "mountpoint": partition.mountpoint,
                        "fstype": partition.fstype,
                        "total": usage.total,
                        "used": usage.used,
                        "free": usage.free,
                        "percentage": (usage.used / usage.total) * 100 if usage.total > 0 else 0
                    })
                except PermissionError:
                    # Ignorer les partitions inaccessibles
                    continue

            return disks
        except Exception as e:
            st.error(f"Erreur lors de la récupération des disques: {e}")
            return []

    def get_network_usage(self) -> Dict:
        """Récupère l'utilisation réseau"""
        try:
            current_stats = psutil.net_io_counters()

            if self.last_network_stats is None:
                self.last_network_stats = current_stats
                return {"bytes_sent_per_sec": 0, "bytes_recv_per_sec": 0}

            # Calcul du débit
            time_diff = 1  # On assume 1 seconde entre les mesures
            bytes_sent_per_sec = (current_stats.bytes_sent - self.last_network_stats.bytes_sent) / time_diff
            bytes_recv_per_sec = (current_stats.bytes_recv - self.last_network_stats.bytes_recv) / time_diff

            self.last_network_stats = current_stats

            return {
                "bytes_sent": current_stats.bytes_sent,
                "bytes_recv": current_stats.bytes_recv,
                "packets_sent": current_stats.packets_sent,
                "packets_recv": current_stats.packets_recv,
                "bytes_sent_per_sec": bytes_sent_per_sec,
                "bytes_recv_per_sec": bytes_recv_per_sec,
                "kb_sent_per_sec": bytes_sent_per_sec / 1024,
                "kb_recv_per_sec": bytes_recv_per_sec / 1024
            }
        except Exception as e:
            st.error(f"Erreur lors de la récupération du réseau: {e}")
            return {}

    def get_processes(self, limit: int = 10) -> List[Dict]:
        """Récupère la liste des processus les plus consommateurs"""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
                try:
                    processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # Trier par utilisation CPU
            processes.sort(key=lambda x: x.get('cpu_percent', 0), reverse=True)
            return processes[:limit]
        except Exception as e:
            st.error(f"Erreur lors de la récupération des processus: {e}")
            return []

    def check_service_status(self, service_name: str, port: int) -> bool:
        """Vérifie si un service est en cours d'exécution sur un port"""
        try:
            connections = psutil.net_connections()
            for conn in connections:
                if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
                    return True
            return False
        except Exception as e:
            st.error(f"Erreur lors de la vérification du service {service_name}: {e}")
            return False

    def update_metrics(self):
        """Met à jour toutes les métriques et l'historique"""
        try:
            timestamp = datetime.now()

            # Récupération des métriques
            cpu_usage = self.get_cpu_usage()
            memory_usage = self.get_memory_usage()
            disk_usage = self.get_disk_usage()
            network_usage = self.get_network_usage()

            # Ajout à l'historique
            self.history["cpu"].append(cpu_usage)
            self.history["memory"].append(memory_usage.get("percentage", 0))

            # Utilisation disque moyenne
            avg_disk_usage = sum(d.get("percentage", 0) for d in disk_usage) / len(disk_usage) if disk_usage else 0
            self.history["disk"].append(avg_disk_usage)

            # Utilisation réseau (en KB/s)
            network_total = network_usage.get("kb_sent_per_sec", 0) + network_usage.get("kb_recv_per_sec", 0)
            self.history["network"].append(network_total)

            self.history["timestamps"].append(timestamp)

            # Limitation de l'historique
            for key in self.history:
                if len(self.history[key]) > self.max_history:
                    self.history[key] = self.history[key][-self.max_history:]

            # Vérification des alertes
            self._check_alerts(cpu_usage, memory_usage.get("percentage", 0), avg_disk_usage, network_total)

        except Exception as e:
            st.error(f"Erreur lors de la mise à jour des métriques: {e}")

    def _check_alerts(self, cpu: float, memory: float, disk: float, network: float):
        """Vérifie et génère les alertes"""
        if not MONITORING_CONFIG["enable_alerts"]:
            return

        current_time = datetime.now()

        # Vérification CPU
        if cpu > CPU_THRESHOLD:
            self._add_alert("high", f"Utilisation CPU élevée: {cpu:.1f}%", "cpu", cpu)

        # Vérification mémoire
        if memory > MEMORY_THRESHOLD:
            self._add_alert("high", f"Utilisation mémoire élevée: {memory:.1f}%", "memory", memory)

        # Vérification disque
        if disk > DISK_THRESHOLD:
            self._add_alert("medium", f"Utilisation disque élevée: {disk:.1f}%", "disk", disk)

        # Vérification réseau
        if network > NETWORK_THRESHOLD:
            self._add_alert("low", f"Trafic réseau élevé: {network:.1f} KB/s", "network", network)

        # Nettoyage des anciennes alertes
        self._cleanup_old_alerts()

    def _add_alert(self, level: str, message: str, metric: str, value: float):
        """Ajoute une alerte"""
        alert = {
            "timestamp": datetime.now(),
            "level": level,
            "message": message,
            "metric": metric,
            "value": value,
            "icon": ALERT_CONFIG["levels"][level]["icon"],
            "color": ALERT_CONFIG["levels"][level]["color"]
        }

        # Éviter les doublons récents (même métrique dans les 30 dernières secondes)
        recent_alerts = [a for a in self.alerts if
                        a["metric"] == metric and
                        (datetime.now() - a["timestamp"]).seconds < 30]

        if not recent_alerts:
            self.alerts.append(alert)

            # Limitation du nombre d'alertes
            if len(self.alerts) > ALERT_CONFIG["max_alerts"]:
                self.alerts = self.alerts[-ALERT_CONFIG["max_alerts"]:]

    def _cleanup_old_alerts(self):
        """Supprime les alertes anciennes"""
        cutoff_time = datetime.now() - timedelta(seconds=ALERT_CONFIG["alert_duration"])
        self.alerts = [alert for alert in self.alerts if alert["timestamp"] > cutoff_time]

    def get_alerts(self, level: Optional[str] = None) -> List[Dict]:
        """Récupère les alertes, optionnellement filtrées par niveau"""
        if level:
            return [alert for alert in self.alerts if alert["level"] == level]
        return self.alerts.copy()

    def clear_alerts(self):
        """Efface toutes les alertes"""
        self.alerts.clear()

    def export_metrics(self, format: str = "csv") -> str:
        """Exporte les métriques dans le format spécifié"""
        try:
            if format == "csv":
                import csv
                import io

                output = io.StringIO()
                writer = csv.writer(output)

                # En-têtes
                writer.writerow(["timestamp", "cpu", "memory", "disk", "network"])

                # Données
                for i in range(len(self.history["timestamps"])):
                    writer.writerow([
                        self.history["timestamps"][i].isoformat(),
                        self.history["cpu"][i] if i < len(self.history["cpu"]) else "",
                        self.history["memory"][i] if i < len(self.history["memory"]) else "",
                        self.history["disk"][i] if i < len(self.history["disk"]) else "",
                        self.history["network"][i] if i < len(self.history["network"]) else ""
                    ])

                return output.getvalue()

        except Exception as e:
            st.error(f"Erreur lors de l'export: {e}")
            return ""


# Instance globale
system_monitor = SystemMonitor()