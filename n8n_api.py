"""
Module pour l'interaction avec l'API n8n
"""

import requests
import json
import time
from typing import Dict, List, Optional, Any
import streamlit as st
from config import N8N_API_URL, N8N_CONFIG


class N8nAPI:
    """Wrapper pour l'API n8n"""

    def __init__(self, base_url: str = N8N_API_URL):
        self.base_url = base_url.rstrip('/')
        self.timeout = N8N_CONFIG["timeout"]
        self.max_retries = N8N_CONFIG["max_retries"]
        self.retry_delay = N8N_CONFIG["retry_delay"]

    def _make_request(self, endpoint: str, method: str = "GET", data: Optional[Dict] = None, params: Optional[Dict] = None) -> requests.Response:
        """Effectue une requête HTTP avec gestion des erreurs et retry"""
        url = f"{self.base_url}{endpoint}"

        for attempt in range(self.max_retries):
            try:
                if method == "GET":
                    response = requests.get(url, timeout=self.timeout, params=params)
                elif method == "POST":
                    response = requests.post(url, json=data, timeout=self.timeout, params=params)
                elif method == "PUT":
                    response = requests.put(url, json=data, timeout=self.timeout, params=params)
                elif method == "DELETE":
                    response = requests.delete(url, timeout=self.timeout, params=params)
                else:
                    raise ValueError(f"Méthode HTTP non supportée: {method}")

                response.raise_for_status()
                return response

            except requests.exceptions.RequestException as e:
                if attempt == self.max_retries - 1:
                    raise e
                time.sleep(self.retry_delay)

        raise Exception("Nombre maximum de tentatives atteint")

    def is_available(self) -> bool:
        """Vérifie si n8n est disponible"""
        try:
            response = self._make_request("/api/v1/workflows")
            return response.status_code == 200
        except:
            return False

    def get_workflows(self) -> List[Dict]:
        """Récupère la liste des workflows"""
        try:
            response = self._make_request("/api/v1/workflows")
            return response.json().get("data", [])
        except Exception as e:
            st.error(f"Erreur lors de la récupération des workflows: {e}")
            return []

    def get_workflow(self, workflow_id: str) -> Optional[Dict]:
        """Récupère un workflow spécifique"""
        try:
            response = self._make_request(f"/api/v1/workflows/{workflow_id}")
            return response.json()
        except Exception as e:
            st.error(f"Erreur lors de la récupération du workflow {workflow_id}: {e}")
            return None

    def activate_workflow(self, workflow_id: str, active: bool = True) -> bool:
        """Active ou désactive un workflow"""
        try:
            data = {"active": active}
            response = self._make_request(f"/api/v1/workflows/{workflow_id}/activate", method="POST", data=data)
            return True
        except Exception as e:
            st.error(f"Erreur lors de l'activation/désactivation du workflow {workflow_id}: {e}")
            return False

    def execute_workflow(self, workflow_id: str, data: Optional[Dict] = None) -> Optional[Dict]:
        """Exécute un workflow manuellement"""
        try:
            execution_data = data or {}
            response = self._make_request(f"/api/v1/workflows/{workflow_id}/execute", method="POST", data=execution_data)
            return response.json()
        except Exception as e:
            st.error(f"Erreur lors de l'exécution du workflow {workflow_id}: {e}")
            return None

    def get_executions(self, workflow_id: Optional[str] = None, limit: int = 20) -> List[Dict]:
        """Récupère la liste des exécutions"""
        try:
            params = {"limit": limit}
            if workflow_id:
                params["workflowId"] = workflow_id

            response = self._make_request("/api/v1/executions", params=params)
            return response.json().get("data", [])
        except Exception as e:
            st.error(f"Erreur lors de la récupération des exécutions: {e}")
            return []

    def get_execution(self, execution_id: str) -> Optional[Dict]:
        """Récupère une exécution spécifique"""
        try:
            response = self._make_request(f"/api/v1/executions/{execution_id}")
            return response.json()
        except Exception as e:
            st.error(f"Erreur lors de la récupération de l'exécution {execution_id}: {e}")
            return None

    def delete_execution(self, execution_id: str) -> bool:
        """Supprime une exécution"""
        try:
            response = self._make_request(f"/api/v1/executions/{execution_id}", method="DELETE")
            return True
        except Exception as e:
            st.error(f"Erreur lors de la suppression de l'exécution {execution_id}: {e}")
            return False

    def get_workflow_statistics(self, workflow_id: str) -> Dict:
        """Récupère les statistiques d'un workflow"""
        try:
            executions = self.get_executions(workflow_id, limit=100)

            stats = {
                "total_executions": len(executions),
                "successful": 0,
                "failed": 0,
                "running": 0,
                "waiting": 0,
                "avg_duration": 0,
                "last_execution": None
            }

            total_duration = 0
            completed_executions = 0

            for execution in executions:
                status = execution.get("finished", False)
                if execution.get("stoppedAt"):
                    if status:
                        stats["successful"] += 1
                    else:
                        stats["failed"] += 1

                    # Calcul de la durée
                    if execution.get("startedAt") and execution.get("stoppedAt"):
                        start = execution["startedAt"]
                        stop = execution["stoppedAt"]
                        # Simplification: on assume que les timestamps sont comparables
                        completed_executions += 1

                elif execution.get("startedAt"):
                    stats["running"] += 1
                else:
                    stats["waiting"] += 1

                # Dernière exécution
                if not stats["last_execution"] or execution.get("startedAt", "") > stats["last_execution"].get("startedAt", ""):
                    stats["last_execution"] = execution

            if completed_executions > 0:
                stats["avg_duration"] = total_duration / completed_executions

            return stats

        except Exception as e:
            st.error(f"Erreur lors du calcul des statistiques: {e}")
            return {}

    def get_active_workflows(self) -> List[Dict]:
        """Récupère la liste des workflows actifs"""
        try:
            workflows = self.get_workflows()
            return [w for w in workflows if w.get("active", False)]
        except Exception as e:
            st.error(f"Erreur lors de la récupération des workflows actifs: {e}")
            return []

    def search_workflows(self, query: str) -> List[Dict]:
        """Recherche des workflows par nom"""
        try:
            workflows = self.get_workflows()
            query_lower = query.lower()
            return [w for w in workflows if query_lower in w.get("name", "").lower()]
        except Exception as e:
            st.error(f"Erreur lors de la recherche de workflows: {e}")
            return []


# Instance globale
n8n_api = N8nAPI()